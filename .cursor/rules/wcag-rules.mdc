---
description: 
globs: 
alwaysApply: false
---
WCAG 2.2 Rules Documentation for IDE Implementation
This document provides a comprehensive guide for implementing Web Content Accessibility Guidelines (WCAG) 2.2 Success Criteria in your application. Each rule is detailed with its level (A, AA, AAA), description, implementation instructions, whether it is detectable by axe-playwright, and whether errors/warnings are instantly findable by <PERSON>wright or similar tools for display in the app. The rules are derived from the provided data and are intended to be actionable for developers working within an IDE. The document is organized by WCAG pillars: Perceivable, Operable, Understandable, Robust, and Conformance.
Perceivable
1.1.1 Non-text Content (Level A)

Description: Provide text alternatives for non-text content (e.g., images, controls, media, CAPTCHA) to support assistive technologies like screen readers.
Implementation:
For images, use the alt attribute with descriptive text (e.g., <img src="logo.png" alt="Company logo">).
For controls or inputs, ensure the aria-label or name attribute describes the purpose (e.g., <button aria-label="Submit form">Submit</button>).
For time-based media (audio/video), provide a text description via aria-describedby or a separate text link.
For CAPTCHAs, offer alternative formats (e.g., audio CAPTCHA) and describe the purpose in text.
For decorative content, use alt="" or aria-hidden="true" to hide from assistive technologies.
Example: <img src="decorative.png" alt=""> or <span aria-hidden="true" class="icon"></span>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (e.g., missing alt attributes can be detected instantly).

1.2.1 Audio-only and Video-only (Prerecorded) (Level A)

Description: Provide alternatives for prerecorded audio-only or video-only content, unless it’s a media alternative for text.
Implementation:
For audio-only, include a text transcript (e.g., <a href="transcript.txt">Audio transcript</a>).
For video-only, provide a text description or audio track with equivalent information.
Ensure alternatives are accessible via links or embedded text near the media.
Example: <video src="intro.mp4" aria-describedby="video-desc"></video><div id="video-desc">Description of video content...</div>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (transcripts/descriptions require human review to verify presence and accuracy).

1.2.2 Captions (Prerecorded) (Level A)

Description: Provide captions for all prerecorded audio in synchronized media, unless it’s a text alternative.
Implementation:
Use <track> element with WebVTT files for captions (e.g., <video><track kind="captions" src="captions.vtt" srclang="en" label="English"></video>).
Ensure captions are accurate and synchronized with audio.
Example: <video controls><source src="video.mp4" type="video/mp4"><track kind="captions" src="captions.vtt" srclang="en" label="English"></video>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (missing <track kind="captions"> can be detected instantly).

1.2.3 Audio Description or Media Alternative (Prerecorded) (Level A)

Description: Provide audio descriptions or a text alternative for prerecorded video in synchronized media.
Implementation:
Include an audio description track using <track kind="descriptions"> or provide a text alternative via a link or aria-describedby.
Example: <video><track kind="descriptions" src="desc.vtt" srclang="en"></video> or <a href="video-desc.txt">Video description</a>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (audio descriptions/text alternatives require human review).

1.2.4 Captions (Live) (Level AA)

Description: Provide captions for all live audio content in synchronized media.
Implementation:
Integrate real-time captioning services (e.g., WebRTC or third-party APIs) for live streams.
Use <track> for captions as in prerecorded media.
Example: <video><track kind="captions" src="live-captions.txt" srclang="en" label="English"></video>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (live captioning presence/accuracy requires human review).

1.2.5 Audio Description (Prerecorded) (Level AA)

Description: Provide audio descriptions for all prerecorded video content in synchronized media.
Implementation:
Embed audio descriptions using <track kind="descriptions">.
Ensure descriptions cover visual content not conveyed by audio.
Example: <video><track kind="descriptions" src="desc.txt"></video>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (audio descriptions require human review for presence and quality).

1.2.6 Sign Language (Prerecorded) (Level AAA)

Description: Provide sign language interpretation for prerecorded audio content in synchronized media.
Implementation:
Include a sign language video track or a separate video linked near the content.
Example: <video><source src="sign-language.mp4" type="video/mp4"></video> or <a href="sign-language.mp4">Sign language version</a>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (sign language content requires human review).

1.2.7 Extended Audio Description (Prerecorded) (Level AAA)

Description: Provide extended audio descriptions when pauses in foreground audio are needed.
Implementation:
Create extended descriptions and link them via a separate audio or text file.
Example: <a href="extended-desc.mp3">Extended audio description</a>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (extended descriptions require human review).

1.2.8 Media Alternative (Prerecorded) (Level AAA)

Description: Provide a text alternative for all prerecorded synchronized or video-only media.
Implementation:
Include a full text transcript accessible via a link or embedded text.
Example: <a href="transcript.txt">Full media transcript</a>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (transcripts require human review for presence and accuracy).

1.2.9 Audio-only (Live) (Level AAA)

Description: Provide a text alternative for live audio-only content.
Implementation:
Use real-time transcription services and display text via a link or dynamic update.
Example: <div id="live-transcript">Live transcription...</div>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (live transcriptions require human review).

1.3.1 Info and Relationships (Level A)

Description: Ensure information, structure, and relationships conveyed visually are programmatically determinable or available in text.
Implementation:
Use semantic HTML (e.g., <header>, <nav>, <main>, <section>, <article>).
Apply ARIA landmarks (e.g., role="navigation", role="main").
Use <label> for form inputs (e.g., <label for="name">Name:</label><input id="name">).
Example: <form><label for="email">Email:</label><input id="email" type="email"></form>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (e.g., missing <label> or non-semantic HTML can be detected).

1.3.2 Meaningful Sequence (Level A)

Description: Ensure the content sequence preserves meaning when programmatically determined.
Implementation:
Maintain logical DOM order matching visual presentation.
Avoid CSS positioning that disrupts reading order (e.g., position: absolute without context).
Example: <div><h1>Title</h1><p>Content...</p></div>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (sequence issues require human review with assistive tech).

1.3.3 Sensory Characteristics (Level A)

Description: Instructions must not rely solely on sensory characteristics (e.g., shape, color, sound).
Implementation:
Combine visual cues with text (e.g., instead of “Click the red button,” use “Click the Submit button (red)”).
Example: <button>Submit (red)</button>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (sensory reliance requires human review).

1.3.4 Orientation (Level AA)

Description: Content must not restrict to a single display orientation unless essential.
Implementation:
Use responsive design (e.g., CSS media queries) to support both portrait and landscape.
Avoid locking orientation with screen.orientation.lock().
Example: body { max-width: 100%; } @media (orientation: portrait) { ... }.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (orientation restrictions require human testing).

1.3.5 Identify Input Purpose (Level AA)

Description: The purpose of input fields collecting user information must be programmatically determinable.
Implementation:
Use HTML5 input types (e.g., <input type="email">) and autocomplete attributes (e.g., <input type="text" autocomplete="name">).
Example: <input type="tel" autocomplete="tel">.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (missing autocomplete attributes can be detected).

1.3.6 Identify Purpose (Level AAA)

Description: The purpose of UI components, icons, and regions must be programmatically determinable.
Implementation:
Use ARIA roles and properties (e.g., role="button", aria-label="Close").
Example: <span role="img" aria-label="Star icon"></span>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (missing ARIA roles/labels can be detected).

1.4.1 Use of Color (Level A)

Description: Information must not be conveyed by color alone.
Implementation:
Add text or patterns alongside color (e.g., use icons or labels with color).
Example: <span style="color: red;">Error: <strong>Invalid input</strong></span>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (color reliance requires human review).

1.4.2 Audio Control (Level A)

Description: Automatically playing audio longer than 3 seconds must have pause/stop or volume controls.
Implementation:
Add controls to <audio> elements (e.g., <audio controls>).
Provide a mute button or stop mechanism.
Example: <audio controls><source src="audio.mp3"></audio>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (missing controls on auto-playing audio can be detected).

1.4.3 Contrast (Minimum) (Level AA)

Description: Text and images of text must have a contrast ratio of at least 4.5:1 (3:1 for large text).
Implementation:
Use tools to check contrast ratios (e.g., WebAIM Contrast Checker).
Set CSS colors (e.g., color: #000; background-color: #FFF; for 21:1 ratio).
Example: p { color: #000; background-color: #FFF; }.


Detectable by axe-playwright: Yes
Instantly Findable by Playwright or Similar Tool: Yes (contrast issues can be detected instantly).

1.4.4 Resize Text (Level AA)

Description: Text must be resizable up to 200% without loss of content or functionality.
Implementation:
Use relative units (e.g., rem, em, %) for font sizes.
Test with browser zoom (e.g., Ctrl + up to 200%).
Example: body { font-size: 1rem; }.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (resize issues require human testing at 200% zoom).

1.4.5 Images of Text (Level AA)

Description: Use text instead of images of text, unless essential or customizable.
Implementation:
Replace images of text with CSS-styled text (e.g., font-family, text-shadow).
Example: <h1 style="font-family: Arial; text-shadow: 2px 2px #000;">Title</h1>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (image text usage requires human review).

1.4.6 Contrast (Enhanced) (Level AAA)

Description: Text and images of text must have a contrast ratio of at least 7:1 (4.5:1 for large text).
Implementation:
Adjust colors to meet higher contrast ratios.
Example: h1 { color: #000; background-color: #FFF; }.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (enhanced contrast issues can be detected with tools).

1.4.7 Low or No Background Audio (Level AAA)

Description: Prerecorded audio with speech must have no background sounds, allow them to be turned off, or keep them 20 dB lower.
Implementation:
Ensure audio files are edited to minimize background noise.
Provide a toggle for background audio.
Example: <audio controls><source src="clean-audio.mp3"></audio>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (background audio issues require human review).

1.4.8 Visual Presentation (Level AAA)

Description: Provide mechanisms for users to customize text presentation (e.g., colors, spacing).
Implementation:
Allow user overrides via CSS custom properties or settings (e.g., --line-height: 1.5;).
Example: :root { --line-height: 1.5; } p { line-height: var(--line-height); }.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (customization issues require human review).

1.4.9 Images of Text (No Exception) (Level AAA)

Description: Images of text are only used for decoration or where essential.
Implementation:
Use text with CSS styling for all non-essential cases.
Example: <span style="font-family: serif;">Decorative text</span>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (image text usage requires human review).

1.4.10 Reflow (Level AA)

Description: Content must reflow without requiring two-dimensional scrolling at 320 CSS pixels width or 256 CSS pixels height.
Implementation:
Use responsive design with CSS (e.g., flex, grid, min-width).
Test at 1280px width with 400% zoom.
Example: @media (max-width: 320px) { body { display: flex; flex-direction: column; } }.


Detectable by axe-playwright: Yes
Instantly Findable by Playwright or Similar Tool: Yes (reflow issues can be detected at specific breakpoints).

1.4.11 Non-text Contrast (Level AA)

Description: UI components and graphical objects must have a 3:1 contrast ratio.
Implementation:
Ensure buttons, icons, and graphics meet contrast requirements.
Example: button { background-color: #333; color: #FFF; }.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (non-text contrast issues can be detected with tools).

1.4.12 Text Spacing (Level AA)

Description: No loss of content or functionality when text spacing is adjusted (line height 1.5x, paragraph spacing 2x, letter spacing 0.12x, word spacing 0.16x).
Implementation:
Use relative units and avoid fixed heights (e.g., line-height: 1.5;).
Example: p { line-height: 1.5; margin-bottom: 2em; }.


Detectable by axe-playwright: Yes
Instantly Findable by Playwright or Similar Tool: Yes (text spacing issues can be detected by applying styles).

1.4.13 Content on Hover or Focus (Level AA)

Description: Additional content on hover or focus must be dismissible, hoverable, and persistent.
Implementation:
Use JavaScript to handle hover/focus events with dismissal options (e.g., Escape key).
Example: <div onmouseover="showTooltip()" onmouseout="hideTooltip()">...</div>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (hover/focus behavior requires human review).

Operable
2.1.1 Keyboard (Level A)

Description: All functionality must be operable via keyboard without specific timing.
Implementation:
Ensure all interactive elements (e.g., <button>, <a>) are focusable with tabindex.
Use onkeydown for keyboard events.
Example: <button tabindex="0" onkeydown="handleKey(event)">Click me</button>.


Detectable by axe-playwright: Yes
Instantly Findable by Playwright or Similar Tool: Yes (non-focusable elements can be detected).

2.1.2 No Keyboard Trap (Level A)

Description: Keyboard focus must not be trapped; users can move focus away using standard keys.
Implementation:
Ensure Tab and Shift+Tab navigate through all focusable elements.
Avoid focus() loops without exit.
Example: <div tabindex="0">Focusable content</div>.


Detectable by axe-playwright: Yes
Instantly Findable by Playwright or Similar Tool: Yes (keyboard traps can be detected by navigation testing).

2.1.3 Keyboard (No Exception) (Level AAA)

Description: All functionality must be keyboard-operable without exceptions.
Implementation:
Remove any path-dependent input requirements.
Example: <input type="text" onkeydown="handleInput(event)">.


Detectable by axe-playwright: Yes
Instantly Findable by Playwright or Similar Tool: Yes (non-keyboard functionality can be detected).

2.1.4 Character Key Shortcuts (Level A)

Description: Keyboard shortcuts using letters, punctuation, or symbols must be turn-offable, remappable, or active only on focus.
Implementation:
Provide a settings UI to disable or remap shortcuts.
Example: <button onclick="disableShortcuts()">Disable shortcuts</button>.


Detectable by axe-playwright: Yes
Instantly Findable by Playwright or Similar Tool: Yes (shortcut issues can be detected by analyzing event listeners).

2.2.1 Timing Adjustable (Level A)

Description: Time limits must be turn-offable, adjustable, or extendable, with exceptions.
Implementation:
Add controls to pause or extend timers (e.g., <button onclick="extendTimer()">Extend</button>).
Example: setTimeout(showWarning, 20000); function showWarning() { alert('Extend time?'); }.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (timer controls require human review).

2.2.2 Pause, Stop, Hide (Level A)

Description: Moving, blinking, or auto-updating content must have pause, stop, or hide mechanisms.
Implementation:
Add buttons to control animations (e.g., <button onclick="stopAnimation()">Stop</button>).
Example: <div id="ticker">...</div><button onclick="document.getElementById('ticker').style.display='none'">Hide</button>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (animation controls require human review).

2.2.3 No Timing (Level AAA)

Description: Timing is not essential except for non-interactive synchronized media and real-time events.
Implementation:
Avoid time-based interactions except for essential cases.
Example: setTimeout not used for critical functions.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (timing necessity requires human review).

2.2.4 Interruptions (Level AAA)

Description: Interruptions can be postponed or suppressed unless emergencies.
Implementation:
Provide user controls to delay notifications.
Example: <button onclick="postponeAlert()">Postpone</button>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (interruption controls require human review).

2.2.5 Re-authenticating (Level AAA)

Description: Users can continue activities without data loss after re-authentication.
Implementation:
Save form data in localStorage during session timeouts.
Example: window.localStorage.setItem('formData', JSON.stringify(formData));.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (data preservation requires human review).

2.2.6 Timeouts (Level AAA)

Description: Warn users about inactivity timeouts that could cause data loss.
Implementation:
Display a warning before session expiry.
Example: <div id="timeout-warning" style="display:none">Session will end soon!</div>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (timeout warnings require human review).

2.3.1 Three Flashes or Below Threshold (Level A)

Description: Avoid content that flashes more than three times per second.
Implementation:
Limit CSS animations (e.g., @keyframes flash { ... }) to safe thresholds.
Example: @keyframes safeFlash { 0%, 100% { opacity: 1; } 50% { opacity: 0; } }.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (flash frequency requires human review).

2.3.2 Three Flashes (Level AAA)

Description: No content flashes more than three times per second.
Implementation:
Disable high-frequency animations.
Example: animation: none;.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (flash absence requires human review).

2.3.3 Animation from Interactions (Level AAA)

Description: Animations triggered by interactions can be disabled.
Implementation:
Provide a toggle for animations (e.g., <button onclick="disableAnimations()">Disable animations</button>).
Example: body.no-animations * { animation: none !important; }.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (animation toggle requires human review).

2.4.1 Bypass Blocks (Level A)

Description: Provide a mechanism to skip repeated content blocks.
Implementation:
Add a “Skip to main content” link (e.g., <a href="#main">Skip to main content</a>).
Example: <a href="#content" class="skip-link">Skip to main content</a>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (missing skip links can be detected).

2.4.2 Page Titled (Level A)

Description: Web pages must have descriptive titles.
Implementation:
Use unique <title> tags (e.g., <title>Home - My Website</title>).
Example: <title>Product Listing - My Store</title>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (missing or non-descriptive <title> can be detected).

2.4.3 Focus Order (Level A)

Description: Focus order must preserve meaning and operability.
Implementation:
Ensure logical tabindex values and DOM order.
Example: <button tabindex="1">First</button><button tabindex="2">Second</button>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (focus order issues require human review).

2.4.4 Link Purpose (In Context) (Level A)

Description: Link purpose must be clear from text or context.
Implementation:
Use descriptive link text (e.g., <a href="about.html">About Us</a>).
Example: <a href="download.pdf">Download Brochure</a>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (vague link text can be detected).

2.4.5 Multiple Ways (Level AA)

Description: Provide multiple ways to locate web pages (e.g., navigation, search).
Implementation:
Include a site map, search bar, and navigation menu.
Example: <input type="search" placeholder="Search site">.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (navigation options require human review).

2.4.6 Headings and Labels (Level AA)

Description: Headings and labels must describe topic or purpose.
Implementation:
Use semantic headings (e.g., <h1>Main Heading</h1>) and clear labels.
Example: <label for="search">Search:</label><input id="search">.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (missing headings/labels can be detected).

2.4.7 Focus Visible (Level AA)

Description: Keyboard focus must be visible.
Implementation:
Style focus states in CSS (e.g., :focus { outline: 2px solid blue; }).
Example: input:focus { outline: 2px solid #00F; }.


Detectable by axe-playwright: Yes
Instantly Findable by Playwright or Similar Tool: Yes (missing focus styles can be detected).

2.4.8 Location (Level AAA)

Description: Provide information about the user’s location within a website.
Implementation:
Use breadcrumbs or a site map (e.g., <nav aria-label="breadcrumb">Home > Products</nav>).
Example: <nav aria-label="breadcrumb">Home > Category > Item</nav>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (missing breadcrumb navigation can be detected).

2.4.9 Link Purpose (Link Only) (Level AAA)

Description: Link purpose must be clear from link text alone.
Implementation:
Avoid vague text like “Click here” (e.g., <a href="contact.html">Contact Us</a>).
Example: <a href="faq.html">Frequently Asked Questions</a>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (vague link text can be detected).

2.4.10 Section Headings (Level AAA)

Description: Use section headings to organize content.
Implementation:
Use <h1> to <h6> appropriately.
Example: <h2>Product Details</h2><p>...</p>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (missing or incorrect headings can be detected).

2.4.11 Focus Not Obscured (Minimum) (Level AA)

Description: Focused components must not be entirely hidden.
Implementation:
Ensure z-index and positioning keep focusable elements visible.
Example: button:focus { z-index: 10; }.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (hidden focus elements can be detected).

2.4.12 Focus Not Obscured (Enhanced) (Level AAA)

Description: No part of focused components is hidden.
Implementation:
Avoid overlapping elements over focused components.
Example: .modal { z-index: 100; }.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (partially hidden focus elements can be detected).

2.4.13 Focus Appearance (Level AAA)

Description: Focus indicators must be at least 2 CSS pixels thick with a 3:1 contrast ratio.
Implementation:
Style focus states (e.g., :focus { outline: 2px solid #000; }).
Example: a:focus { outline: 2px solid black; }.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (inadequate focus styles can be detected).

2.5.1 Pointer Gestures (Level A)

Description: Functionality using multipoint or path-based gestures must have single-pointer alternatives.
Implementation:
Provide button alternatives for gestures (e.g., <button onclick="zoom()">Zoom</button>).
Example: <button onclick="swipeNext()">Next</button>.


Detectable by axe-playwright: Yes
Instantly Findable by Playwright or Similar Tool: Yes (missing gesture alternatives can be detected).

2.5.2 Pointer Cancellation (Level A)

Description: Single-pointer actions must be cancellable or reversible.
Implementation:
Use onmouseup instead of onmousedown for actions.
Example: <button onmouseup="confirmAction()">Confirm</button>.


Detectable by axe-playwright: Yes
Instantly Findable by Playwright or Similar Tool: Yes (non-cancellable actions can be detected).

2.5.3 Label in Name (Level A)

Description: UI component names must include their visible label text.
Implementation:
Ensure aria-label or name matches visible text (e.g., <button aria-label="Submit">Submit</button>).
Example: <input id="search" aria-label="Search">Search</input>.


Detectable by axe-playwright: Yes
Instantly Findable by Playwright or Similar Tool: Yes (mismatched labels can be detected).

2.5.4 Motion Actuation (Level A)

Description: Motion-based functionality must have alternatives and be disableable.
Implementation:
Provide button controls for motion (e.g., <button onclick="shake()">Shake</button>).
Example: <button onclick="disableMotion()">Disable motion</button>.


Detectable by axe-playwright: Yes
Instantly Findable by Playwright or Similar Tool: Yes (missing motion alternatives can be detected).

2.5.5 Target Size (Enhanced) (Level AAA)

Description: Pointer targets must be at least 44x44 CSS pixels.
Implementation:
Set minimum dimensions (e.g., button { width: 44px; height: 44px; }).
Example: a { min-width: 44px; min-height: 44px; display: inline-block; }.


Detectable by axe-playwright: Yes
Instantly Findable by Playwright or Similar Tool: Yes (small target sizes can be detected).

2.5.6 Concurrent Input Mechanisms (Level AAA)

Description: Do not restrict input modalities unless essential.
Implementation:
Support mouse, keyboard, and touch inputs.
Example: <button onclick="action()" onkeydown="action()">Action</button>.


Detectable by axe-playwright: Yes
Instantly Findable by Playwright or Similar Tool: Yes (restricted inputs can be detected).

2.5.7 Dragging Movements (Level AA)

Description: Dragging functionality must have non-dragging alternatives.
Implementation:
Provide buttons for drag actions (e.g., <button onclick="moveItem()">Move</button>).
Example: <div draggable="true" ondragend="handleDrag()">...</div>.


Detectable by axe-playwright: Yes
Instantly Findable by Playwright or Similar Tool: Yes (missing drag alternatives can be detected).

2.5.8 Target Size (Minimum) (Level AA)

Description: Pointer targets must be at least 24x24 CSS pixels.
Implementation:
Set minimum dimensions (e.g., button { width: 24px; height: 24px; }).
Example: a { min-width: 24px; min-height: 24px; }.


Detectable by axe-playwright: Yes
Instantly Findable by Playwright or Similar Tool: Yes (small target sizes can be detected).

Understandable
3.1.1 Language of Page (Level A)

Description: The default language of the page must be programmatically determinable.
Implementation:
Set the lang attribute on <html> (e.g., <html lang="en">).
Example: <html lang="en-US">.


Detectable by axe-playwright: Yes
Instantly Findable by Playwright or Similar Tool: Yes (missing lang attribute can be detected).

3.1.2 Language of Parts (Level AA)

Description: Language changes within content must be programmatically determinable.
Implementation:
Use lang on elements (e.g., <span lang="es">Hola</span>).
Example: <p>English text <span lang="fr">Bonjour</span></p>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (missing lang for language changes).

3.1.3 Unusual Words (Level AAA)

Description: Provide definitions for unusual words or phrases.
Implementation:
Use <abbr> or tooltips (e.g., <abbr title="World Wide Web Consortium">W3C</abbr>).
Example: <span title="Technical term">Jargon</span>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (unusual words require human review).

3.1.4 Abbreviations (Level AAA)

Description: Provide expanded forms of abbreviations.
Implementation:
Use <abbr> (e.g., <abbr title="HyperText Markup Language">HTML</abbr>).
Example: <abbr title="Cascading Style Sheets">CSS</abbr>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (missing <abbr> for abbreviations can be detected).

3.1.5 Reading Level (Level AAA)

Description: Text must not require reading ability beyond lower secondary level.
Implementation:
Simplify language or provide a simplified version.
Example: <a href="simple-version.html">Simplified version</a>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (reading level requires human review).

3.1.6 Pronunciation (Level AAA)

Description: Provide pronunciation for ambiguous words.
Implementation:
Use <ruby> or tooltips (e.g., <ruby>read<rt>reed</rt></ruby>).
Example: <span title="Pronunciation: kon-tent">content</span>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (ambiguous words require human review).

3.2.1 On Focus (Level A)

Description: Focus must not initiate a change of context.
Implementation:
Avoid onfocus events that redirect or alter content.
Example: <input onfocus="doNothing()">.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (context-changing onfocus events can be detected).

3.2.2 On Input (Level A)

Description: Input changes must not automatically cause context changes without warning.
Implementation:
Notify users before input actions (e.g., <input onchange="warnUser()">).
Example: function warnUser() { alert('This will change the page.'); }.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (context-changing onchange events can be detected).

3.2.3 Consistent Navigation (Level AA)

Description: Navigation mechanisms must appear in the same order across pages.
Implementation:
Use consistent <nav> structures.
Example: <nav><a href="home.html">Home</a><a href="about.html">About</a></nav>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (navigation consistency requires human review).

3.2.4 Consistent Identification (Level AA)

Description: Components with the same functionality must be consistently identified.
Implementation:
Use consistent IDs and labels (e.g., <button id="submit">Submit</button>).
Example: <a id="back" href="previous.html">Back</a>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (identification consistency requires human review).

3.2.5 Change on Request (Level AAA)

Description: Context changes must be user-initiated or disableable.
Implementation:
Provide confirmation dialogs (e.g., <button onclick="confirmChange()">Change</button>).
Example: function confirmChange() { if (confirm('Proceed?')) { ... } }.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (automatic context changes can be detected).

3.2.6 Consistent Help (Level A)

Description: Help mechanisms must appear in the same order across pages.
Implementation:
Place help links consistently (e.g., <a href="help.html">Help</a>).
Example: <footer><a href="contact.html">Contact Support</a></footer>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (help consistency requires human review).

3.3.1 Error Identification (Level A)

Description: Input errors must be identified and described in text.
Implementation:
Use aria-invalid and error messages (e.g., <input aria-invalid="true" aria-describedby="error">).
Example: <input id="email" aria-describedby="email-error"><div id="email-error">Invalid email</div>.


Detectable by axe-playwright: Yes
Instantly Findable by Playwright or Similar Tool: Yes (missing aria-invalid or error messages can be detected).

3.3.2 Labels or Instructions (Level A)

Description: Provide labels or instructions for user input.
Implementation:
Use <label> or aria-label (e.g., <label for="username">Username:</label>).
Example: <input id="password" aria-label="Password">.


Detectable by axe-playwright: Yes
Instantly Findable by Playwright or Similar Tool: Yes (missing labels can be detected).

3.3.3 Error Suggestion (Level AA)

Description: Provide correction suggestions for input errors.
Implementation:
Display suggestions via JavaScript (e.g., <div id="suggestion">Did you mean...?</div>).
Example: if (input.value === '') { suggestion.textContent = 'Field required'; }.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (error suggestions require human review).

3.3.4 Error Prevention (Legal, Financial, Data) (Level AA)

Description: Prevent errors in legal, financial, or data submissions.
Implementation:
Add confirmation steps (e.g., <button onclick="confirmSubmit()">Submit</button>).
Example: function confirmSubmit() { if (confirm('Confirm transaction?')) { ... } }.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (error prevention measures require human review).

3.3.5 Help (Level AAA)

Description: Provide context-sensitive help.
Implementation:
Include help tooltips or links (e.g., <a href="help.html">?</a>).
Example: <span title="Enter your email">?</span>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (help presence requires human review).

3.3.6 Error Prevention (All) (Level AAA)

Description: Prevent errors for all submissions.
Implementation:
Validate inputs and provide confirmation (e.g., <form onsubmit="validateForm()">).
Example: function validateForm() { return checkInputs(); }.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: No (validation measures require human review).

3.3.7 Redundant Entry (Level A)

Description: Auto-populate or allow selection of previously entered data.
Implementation:
Use autocomplete or localStorage (e.g., <input autocomplete="on">).
Example: input.value = localStorage.getItem('username');.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (missing autocomplete can be detected).

3.3.8 Accessible Authentication (Minimum) (Level AA)

Description: Avoid cognitive function tests for authentication unless alternatives are provided.
Implementation:
Support password managers or alternative methods (e.g., <input type="password" autocomplete="current-password">).
Example: <button onclick="useBiometrics()">Login with biometrics</button>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (missing authentication alternatives can be detected).

3.3.9 Accessible Authentication (Enhanced) (Level AAA)

Description: No cognitive function tests for authentication without alternatives.
Implementation:
Provide non-cognitive authentication (e.g., email links).
Example: <a href="email-login.html">Email login link</a>.


Detectable by axe-playwright: No
Instantly Findable by Playwright or Similar Tool: Yes (missing non-cognitive alternatives can be detected).

Robust
4.1.2 Name, Role, Value (Level A)

Description: UI components must have programmatically determinable names, roles, and values.
Implementation:
Use ARIA attributes (e.g., role="button" aria-label="Close").
Example: <div role="checkbox" aria-checked="false">Check</div>.


Detectable by axe-playwright: Yes
Instantly Findable by Playwright or Similar Tool: Yes (missing ARIA attributes can be detected).

4.1.3 Status Messages (Level AA)

Description: Status messages must be programmatically determinable without focus.
Implementation:
Use role="status" or aria-live (e.g., <div role="status">Updated</div>).
Example: <div aria-live="polite">Form submitted</div>.


Detectable by axe-playwright: Yes
Instantly Findable by Playwright or Similar Tool: Yes (missing aria-live can be detected).

Conformance
5.2.1 Conformance Level

Description: Meet all Success Criteria for the desired level (A, AA, or AAA).
Implementation:
Validate code against WCAG 2.2 using tools like axe-playwright.
Ensure all Level A and AA rules are met for AA conformance.


Detectable by axe-playwright: Not applicable
Instantly Findable by Playwright or Similar Tool: No (conformance requires multiple checks, some needing human review).

5.2.2 Full Pages

Description: Entire pages, including variations, must conform.
Implementation:
Test all responsive breakpoints and dynamic content.
Example: @media (max-width: 600px) { ... }.


Detectable by axe-playwright: Not applicable
Instantly Findable by Playwright or Similar Tool: No (full-page conformance requires human review).

5.2.3 Complete Processes

Description: All pages in a process must conform.
Implementation:
Ensure consistency across multi-step processes (e.g., checkout flows).
Example: <form action="step2.html">.


Detectable by axe-playwright: Not applicable
Instantly Findable by Playwright or Similar Tool: No (process conformance requires human review).

5.2.4 Only Accessibility-Supported Ways

Description: Use accessibility-supported technologies.
Implementation:
Stick to standard HTML, CSS, and JavaScript.
Example: Avoid deprecated attributes like align.


Detectable by axe-playwright: Not applicable
Instantly Findable by Playwright or Similar Tool: Yes (non-supported tech can be detected).

5.2.5 Non-Interference

Description: Non-accessible content must not interfere with page accessibility.
Implementation:
Ensure compliance with 1.4.2, 2.1.2, 2.3.1, and 2.2.2.
Example: <audio controls> for audio control.


Detectable by axe-playwright: Not applicable
Instantly Findable by Playwright or Similar Tool: No (interference requires human review).


Notes:

Instantly Findable by Playwright or Similar Tool: Indicates errors/warnings detectable by automated tools like axe-playwright for immediate display in the app. Marked "No" for rules requiring human review (e.g., visual, audio, video, or contextual issues).
Automatable Rules: Rules marked “Yes” under “Automatable” in the source data align with “Instantly Findable” where applicable.
Samya Compatibility: Rules marked “Yes” under “With Samya” are supported by the Samya platform.
Testing: Use tools like axe-playwright for instantly findable rules and complement with human testing (e.g., NVDA, VoiceOver) for others.
Privacy and Security: Pay attention to rules like 2.2.6 and 3.3.7 for user data protection.

This documentation should be integrated into your IDE’s reference system for ongoing development and validation.
