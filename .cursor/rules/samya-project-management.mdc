---
description: 
globs: 
alwaysApply: false
---
<PERSON><PERSON> – Product & Business Strategy

This is just the idea about the overall app

📌 Company Overview
Mission:
 To empower businesses and developers to create inclusive digital experiences easily and efficiently.
Vision:
 A world where digital accessibility is standard, not a special feature — made possible through AI and self-service tools.

💎 Value Proposition
Samya delivers an AI-powered, real-time, self-service, and affordable accessibility solution for websites and mobile applications.
Key Differentiators:
Real-time Analysis: Instantly detect and flag accessibility violations.


Self-Service Platform: No need for deep technical knowledge.


Affordable Pricing: Designed to serve startups and enterprises alike.


Comprehensive Assessments: Adheres to WCAG, ADA, Section 508, and EAA standards.


AI-Driven Insights: Actionable recommendations powered by intelligent algorithms.



🎯 Target Market
Samya targets digital product creators and organizations that require accessible online experiences, including:
Web and mobile app developers


Small and medium-sized businesses (SMBs)


E-commerce platforms


Educational institutions


Government agencies and municipalities


Accessibility-focused development agencies



📈 Why Now?
⚖️ Global Regulatory Pressure
The European Accessibility Act (EAA) takes effect in June 2025, requiring accessibility for websites, mobile apps, ATMs, e-commerce services, transport apps, and more.


Applies to all businesses selling in the EU, even if headquartered elsewhere.


Non-compliance may result in fines, product bans, and reputational harm.


Aligned with WCAG 2.1, it raises the technical bar for accessibility compliance.


In the U.S., over 4,000 lawsuits were filed in 2023 for digital accessibility violations (ADA compliance), and enforcement is expanding.


📉 Current Market Gaps
Existing solutions are:


Cost-prohibitive for small businesses.


Not real-time, leading to delays in remediation.


Overly technical, requiring specialist knowledge.


💡 Opportunity
Accessibility is becoming a competitive differentiator.


Inclusive design benefits all users — boosting SEO, conversion rates, and customer satisfaction.


Most businesses lack the tools and expertise to meet compliance deadlines on their own.



⚙️ Product Features
Samya’s platform is built to integrate directly into developers’ workflows, with minimal friction:
🔍 Automated Accessibility Scanning


📊 Detailed Reports on violations and improvement suggestions


⬇️ Exportable Reports (via API or manual download)


🔗 Integrations:


JIRA and project management tools


CI/CD pipelines with configurable fail rules


IDE plug-ins for one-click fixes


🧠 AI-Powered Recommendations


📈 User Dashboard for real-time monitoring and historical tracking



🧭 Business Goals
Establish Samya as a leader in AI-driven digital accessibility.


Achieve profitability within 3 months of launch.


Build strategic partnerships with development firms and accessibility advocates.


Continuously enhance product capabilities based on user feedback and regulatory changes.



📢 Marketing Strategy
🧠 Content Marketing: Blog posts, guides, and webinars to educate developers.


🚀 SEO & Online Advertising: Targeted ads and organic search visibility.


💬 Community Engagement: Forums, LinkedIn, developer Slack groups.


🧩 Event Participation: Speak at tech and accessibility conferences.


🌱 Guerrilla & Word-of-Mouth Marketing: Promote through accessibility and dev communities.



💰 Financial Projections
Revenue Model:


Monthly subscription for SMBs.


Tiered enterprise packages with additional support and customization.


Profitability Target:


Lean operational model aiming for profitability within 3 months post-MVP.


Cost Management:


Focused team, cloud infrastructure optimization, automation in support and onboarding.



📊 Competitive Analysis



TPGi
Evinced
Deque Systems
Samya
Founded
**************
2025
Core Technology
ARC Platform, JAWS Solutions
AI & Computer Vision
axe-core Engine
AI-Powered Real-Time Analysis
Target Market
SMBs to Large Enterprises
Large Enterprises
SMBs to Large Enterprises
SMBs to Mid-Market Businesses
Ease of Use
Moderate
Moderate
Moderate to High
High
Pricing Transparency
Low
Low
Moderate
High
Integration Capabilities
Extensive (Dev Tools, APIs)
IDEs, Dev Workflows
IDEs, CI/CD, Training Platforms
IDEs, CI/CD, Project Management Tools
Support & Training
Extensive
Limited
Extensive (Deque University)
Moderate
Unique Selling Point
Comprehensive Services & Tools
Advanced AI Detection
Comprehensive Toolset & Training
Affordable, Self-Service Platform


👥 Team
Sukriti Chadha


Vinayak Dhadda 

🛣️ Development Roadmap
Samya Master Sheet

MVP (for web and native mobile)

Everything that is testable with automated testing (without AI) is testable with Samya according to WCAG 2.1
The remediation reports highlight the elements, errors and suggestion fixes
Reports are exportable as PDF, CSV 

V0

Scans span all pages that the AI can navigate through
Reports are exportable through API and integrate with JIRA
Dashboard shows historical compliance trends
Slack integrations
One-click automated fixes or suggestions
Configurable scanning based on WCAG guideline versions
Enterprise SSO and SOC 2 compliance
Payment gateways

V1

Tests not just compliance, but overall usability with assistive technology
Figma integration 
CI/CD integration with automated failures upon critical errors
Developer-level tracking of shipping accessibility compliant code
Certification of accessibility
Manual tests to augment the above

V2

Expand to Flutter/ React Native apps


























	
https://playwright.dev/docs/accessibility-testing

🔷 1. Perceivable
Image alt text (image-alt)


Form label presence (label)


Color contrast (color-contrast)


Audio/video captions


ARIA roles for landmarks (region)


Page title (document-title)


Text in links and buttons (link-name, button-name)


Heading order (heading-order)



🔷 2. Operable
Keyboard access and focus (keyboard, tabindex, focus-order-semantics)


Link purpose (link-name)


Skip links (skip-link)


No keyboard trap (no-keyboard-trap)


ARIA hidden on focusable elements (aria-hidden-focus)



🔷 3. Understandable
Language of page set (html-has-lang)


Form inputs have clear labels (label, form-field-multiple-labels)


Buttons have descriptive names (button-name)


Use of autocomplete on forms (autocomplete-valid)



🔷 4. Robust
ARIA roles/attributes valid (aria-roles, aria-valid-attr, aria-valid-attr-value)


Elements have unique IDs (duplicate-id)


Landmark roles correctly used (landmark-one-main, region)



📦 Example of Rule Structure
Each rule has:
ID (e.g., color-contrast)


WCAG tags (e.g., wcag21aa, wcag1411)


Impact level: minor, moderate, serious, or critical


Help URL: documentation for fixing the issue

