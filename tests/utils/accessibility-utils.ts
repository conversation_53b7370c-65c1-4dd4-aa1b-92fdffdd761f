import { Page } from '@playwright/test';

/**
 * Utility functions for WCAG 2.2 accessibility testing
 */

export interface AccessibilityTestResult {
  rule: string;
  level: 'A' | 'AA' | 'AAA';
  status: 'pass' | 'fail' | 'manual' | 'not-applicable';
  description: string;
  elements?: string[];
  manualSteps?: string[];
}

export class AccessibilityTester {
  constructor(private page: Page) {}

  /**
   * Check for missing alt attributes on images (1.1.1)
   */
  async checkImageAltText(): Promise<AccessibilityTestResult> {
    const images = await this.page.locator('img').all();
    const violatingElements: string[] = [];

    for (const img of images) {
      const alt = await img.getAttribute('alt');
      const ariaHidden = await img.getAttribute('aria-hidden');
      const role = await img.getAttribute('role');
      const src = await img.getAttribute('src');

      if (alt === null && ariaHidden !== 'true' && role !== 'presentation') {
        violatingElements.push(`img[src="${src}"]`);
      }
    }

    return {
      rule: '1.1.1',
      level: 'A',
      status: violatingElements.length > 0 ? 'fail' : 'pass',
      description: 'Non-text Content - Images must have alt attributes',
      elements: violatingElements
    };
  }

  /**
   * Check for proper heading hierarchy (2.4.6, 2.4.10)
   */
  async checkHeadingHierarchy(): Promise<AccessibilityTestResult> {
    const headings = await this.page.locator('h1, h2, h3, h4, h5, h6').all();
    const violatingElements: string[] = [];

    if (headings.length === 0) {
      return {
        rule: '2.4.6',
        level: 'AA',
        status: 'not-applicable',
        description: 'Headings and Labels - No headings found'
      };
    }

    // Check for h1
    const h1Count = await this.page.locator('h1').count();
    if (h1Count === 0) {
      violatingElements.push('Missing h1 element');
    }

    // Check heading order
    const headingLevels: number[] = [];
    for (const heading of headings) {
      const tagName = await heading.evaluate(el => el.tagName.toLowerCase());
      const level = parseInt(tagName.charAt(1));
      headingLevels.push(level);
    }

    // Simplified check: first heading should be h1
    if (headingLevels.length > 0 && headingLevels[0] !== 1) {
      violatingElements.push('First heading is not h1');
    }

    return {
      rule: '2.4.6',
      level: 'AA',
      status: violatingElements.length > 0 ? 'fail' : 'pass',
      description: 'Headings and Labels - Proper heading hierarchy',
      elements: violatingElements
    };
  }

  /**
   * Check for descriptive link text (2.4.4, 2.4.9)
   */
  async checkLinkText(): Promise<AccessibilityTestResult> {
    const links = await this.page.locator('a[href]').all();
    const violatingElements: string[] = [];

    for (const link of links) {
      const text = await link.textContent();
      const ariaLabel = await link.getAttribute('aria-label');
      const title = await link.getAttribute('title');
      const href = await link.getAttribute('href');

      const linkText = text?.trim() || ariaLabel || title || '';

      if (linkText.length === 0) {
        violatingElements.push(`a[href="${href}"] - Empty link text`);
      } else {
        // Check for generic link text
        const genericTexts = ['click here', 'read more', 'more', 'link', 'here'];
        const isGeneric = genericTexts.some(generic => 
          linkText.toLowerCase() === generic.toLowerCase()
        );
        
        if (isGeneric) {
          violatingElements.push(`a[href="${href}"] - Generic link text: "${linkText}"`);
        }
      }
    }

    return {
      rule: '2.4.4',
      level: 'A',
      status: violatingElements.length > 0 ? 'fail' : 'pass',
      description: 'Link Purpose (In Context) - Links must have descriptive text',
      elements: violatingElements
    };
  }

  /**
   * Check for form labels (3.3.2)
   */
  async checkFormLabels(): Promise<AccessibilityTestResult> {
    const inputs = await this.page.locator('input, select, textarea').all();
    const violatingElements: string[] = [];

    for (const input of inputs) {
      const type = await input.getAttribute('type');
      
      // Skip hidden inputs
      if (type === 'hidden') continue;

      const id = await input.getAttribute('id');
      const ariaLabel = await input.getAttribute('aria-label');
      const ariaLabelledby = await input.getAttribute('aria-labelledby');
      const name = await input.getAttribute('name');

      // Check for associated label
      let hasLabel = false;
      
      if (id) {
        const labelCount = await this.page.locator(`label[for="${id}"]`).count();
        hasLabel = labelCount > 0;
      }

      // Input should have label, aria-label, aria-labelledby, or be a button
      const isButton = type === 'submit' || type === 'button';
      
      if (!hasLabel && !ariaLabel && !ariaLabelledby && !isButton) {
        violatingElements.push(`input[name="${name}"][type="${type}"] - Missing label`);
      }
    }

    return {
      rule: '3.3.2',
      level: 'A',
      status: violatingElements.length > 0 ? 'fail' : 'pass',
      description: 'Labels or Instructions - Form inputs must have labels',
      elements: violatingElements
    };
  }

  /**
   * Check for minimum target sizes (2.5.8)
   */
  async checkTargetSizes(): Promise<AccessibilityTestResult> {
    const interactiveElements = await this.page.locator(
      'button, a, input[type="submit"], input[type="button"], [role="button"], [tabindex="0"]'
    ).all();
    
    const violatingElements: string[] = [];

    for (const element of interactiveElements) {
      const isVisible = await element.isVisible();
      if (isVisible) {
        const boundingBox = await element.boundingBox();
        
        if (boundingBox) {
          // WCAG 2.2 Level AA requires minimum 24x24 CSS pixels
          if (boundingBox.width < 24 || boundingBox.height < 24) {
            const tagName = await element.evaluate(el => el.tagName.toLowerCase());
            const text = await element.textContent();
            violatingElements.push(
              `${tagName} - Size: ${Math.round(boundingBox.width)}x${Math.round(boundingBox.height)}px - Text: "${text?.slice(0, 30)}"`
            );
          }
        }
      }
    }

    return {
      rule: '2.5.8',
      level: 'AA',
      status: violatingElements.length > 0 ? 'fail' : 'pass',
      description: 'Target Size (Minimum) - Interactive elements must be at least 24x24px',
      elements: violatingElements
    };
  }

  /**
   * Check for skip links (2.4.1)
   */
  async checkSkipLinks(): Promise<AccessibilityTestResult> {
    const skipLinks = await this.page.locator('a[href*="#"], a[href*="skip"], a[href*="main"]').all();
    
    let hasSkipToMain = false;
    for (const link of skipLinks) {
      const text = await link.textContent();
      const href = await link.getAttribute('href');
      
      if (text?.toLowerCase().includes('skip') || href?.includes('#main') || href?.includes('#content')) {
        hasSkipToMain = true;
        break;
      }
    }

    // Check if page has navigation that would require skip links
    const navCount = await this.page.locator('nav, [role="navigation"]').count();
    
    return {
      rule: '2.4.1',
      level: 'A',
      status: navCount > 0 && !hasSkipToMain ? 'fail' : 'pass',
      description: 'Bypass Blocks - Pages with navigation should have skip links',
      elements: navCount > 0 && !hasSkipToMain ? ['Missing skip to main content link'] : []
    };
  }

  /**
   * Run all automated accessibility checks
   */
  async runAllChecks(): Promise<AccessibilityTestResult[]> {
    const results: AccessibilityTestResult[] = [];

    results.push(await this.checkImageAltText());
    results.push(await this.checkHeadingHierarchy());
    results.push(await this.checkLinkText());
    results.push(await this.checkFormLabels());
    results.push(await this.checkTargetSizes());
    results.push(await this.checkSkipLinks());

    return results;
  }
}

/**
 * Generate a comprehensive accessibility report
 */
export function generateAccessibilityReport(results: AccessibilityTestResult[]): string {
  const passed = results.filter(r => r.status === 'pass');
  const failed = results.filter(r => r.status === 'fail');
  const manual = results.filter(r => r.status === 'manual');
  const notApplicable = results.filter(r => r.status === 'not-applicable');

  let report = '\n📊 WCAG 2.2 Accessibility Test Report\n';
  report += '=====================================\n\n';
  
  report += `✅ Passed: ${passed.length}\n`;
  report += `❌ Failed: ${failed.length}\n`;
  report += `⚠️  Manual Review Required: ${manual.length}\n`;
  report += `➖ Not Applicable: ${notApplicable.length}\n\n`;

  if (failed.length > 0) {
    report += '❌ FAILED TESTS:\n';
    report += '================\n';
    failed.forEach(result => {
      report += `\n${result.rule} (Level ${result.level}): ${result.description}\n`;
      if (result.elements && result.elements.length > 0) {
        result.elements.forEach(element => {
          report += `  - ${element}\n`;
        });
      }
    });
  }

  if (manual.length > 0) {
    report += '\n⚠️  MANUAL REVIEW REQUIRED:\n';
    report += '===========================\n';
    manual.forEach(result => {
      report += `\n${result.rule} (Level ${result.level}): ${result.description}\n`;
      if (result.manualSteps && result.manualSteps.length > 0) {
        result.manualSteps.forEach(step => {
          report += `  - ${step}\n`;
        });
      }
    });
  }

  return report;
}
