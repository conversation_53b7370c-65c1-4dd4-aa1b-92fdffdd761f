import { test, expect } from '@playwright/test';

/**
 * Custom WCAG 2.2 Accessibility Tests
 * Tests rules that are "Instantly Findable" but not detectable by axe-playwright
 */

const pages = [
  { name: 'Home', url: '/' },
  { name: 'Pricing', url: '/pricing' },
  { name: 'Results', url: '/results' },
];

for (const page of pages) {
  test.describe(`Custom WCAG 2.2 Tests - ${page.name} Page`, () => {
    
    test(`should have alt attributes for all images (1.1.1) on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      // Find all images
      const images = await playwright.locator('img').all();
      
      for (const img of images) {
        const alt = await img.getAttribute('alt');
        const ariaHidden = await img.getAttribute('aria-hidden');
        const role = await img.getAttribute('role');
        
        // Images should have alt attribute or be properly hidden
        expect(
          alt !== null || ariaHidden === 'true' || role === 'presentation'
        ).toBeTruthy();
      }
    });

    test(`should have proper page title (2.4.2) on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      const title = await playwright.title();
      
      // Title should exist and be descriptive (not empty or generic)
      expect(title).toBeTruthy();
      expect(title.length).toBeGreaterThan(0);
      expect(title.toLowerCase()).not.toBe('untitled');
      expect(title.toLowerCase()).not.toBe('page');
    });

    test(`should have semantic heading structure (2.4.6, 2.4.10) on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      // Check for proper heading hierarchy
      const headings = await playwright.locator('h1, h2, h3, h4, h5, h6').all();
      
      if (headings.length > 0) {
        // Should have at least one h1
        const h1Count = await playwright.locator('h1').count();
        expect(h1Count).toBeGreaterThanOrEqual(1);
        
        // Check heading order (simplified check)
        const headingLevels = [];
        for (const heading of headings) {
          const tagName = await heading.evaluate(el => el.tagName.toLowerCase());
          const level = parseInt(tagName.charAt(1));
          headingLevels.push(level);
        }
        
        // First heading should be h1
        expect(headingLevels[0]).toBe(1);
      }
    });

    test(`should have descriptive link text (2.4.4, 2.4.9) on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      const links = await playwright.locator('a[href]').all();
      
      for (const link of links) {
        const text = await link.textContent();
        const ariaLabel = await link.getAttribute('aria-label');
        const title = await link.getAttribute('title');
        
        const linkText = text?.trim() || ariaLabel || title || '';
        
        // Links should have descriptive text
        expect(linkText.length).toBeGreaterThan(0);
        
        // Avoid generic link text
        const genericTexts = ['click here', 'read more', 'more', 'link', 'here'];
        const isGeneric = genericTexts.some(generic => 
          linkText.toLowerCase().includes(generic) && linkText.toLowerCase().length <= generic.length + 5
        );
        expect(isGeneric).toBeFalsy();
      }
    });

    test(`should have proper form input labels (1.3.1) on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      const inputs = await playwright.locator('input, select, textarea').all();
      
      for (const input of inputs) {
        const type = await input.getAttribute('type');
        
        // Skip hidden inputs
        if (type === 'hidden') continue;
        
        const id = await input.getAttribute('id');
        const ariaLabel = await input.getAttribute('aria-label');
        const ariaLabelledby = await input.getAttribute('aria-labelledby');
        const placeholder = await input.getAttribute('placeholder');
        
        // Check for associated label
        let hasLabel = false;
        
        if (id) {
          const labelCount = await playwright.locator(`label[for="${id}"]`).count();
          hasLabel = labelCount > 0;
        }
        
        // Input should have label, aria-label, aria-labelledby, or be properly described
        expect(
          hasLabel || ariaLabel || ariaLabelledby || (type === 'submit' || type === 'button')
        ).toBeTruthy();
      }
    });

    test(`should have autocomplete attributes where appropriate (1.3.5) on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      // Common input types that should have autocomplete
      const inputsNeedingAutocomplete = await playwright.locator(
        'input[type="email"], input[type="tel"], input[type="url"], input[name*="name"], input[name*="email"], input[name*="phone"]'
      ).all();
      
      for (const input of inputsNeedingAutocomplete) {
        const autocomplete = await input.getAttribute('autocomplete');
        const type = await input.getAttribute('type');
        const name = await input.getAttribute('name');
        
        // Email inputs should have autocomplete="email"
        if (type === 'email' || name?.includes('email')) {
          expect(autocomplete).toBeTruthy();
        }
        
        // Tel inputs should have autocomplete="tel"
        if (type === 'tel' || name?.includes('phone') || name?.includes('tel')) {
          expect(autocomplete).toBeTruthy();
        }
      }
    });

    test(`should have skip links for navigation (2.4.1) on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      // Look for skip links (usually hidden but focusable)
      const skipLinks = await playwright.locator('a[href*="#"], a[href*="skip"], a[href*="main"]').all();
      
      let hasSkipToMain = false;
      for (const link of skipLinks) {
        const text = await link.textContent();
        const href = await link.getAttribute('href');
        
        if (text?.toLowerCase().includes('skip') || href?.includes('#main') || href?.includes('#content')) {
          hasSkipToMain = true;
          break;
        }
      }
      
      // For pages with navigation, should have skip links
      const navCount = await playwright.locator('nav, [role="navigation"]').count();
      if (navCount > 0) {
        expect(hasSkipToMain).toBeTruthy();
      }
    });

    test(`should have proper ARIA roles and labels (1.3.6) on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      // Check buttons have proper names
      const buttons = await playwright.locator('button, [role="button"]').all();
      
      for (const button of buttons) {
        const text = await button.textContent();
        const ariaLabel = await button.getAttribute('aria-label');
        const ariaLabelledby = await button.getAttribute('aria-labelledby');
        
        const buttonName = text?.trim() || ariaLabel || '';
        
        // Buttons should have accessible names
        expect(buttonName.length).toBeGreaterThan(0);
      }
    });

    test(`should not have auto-playing audio without controls (1.4.2) on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      const audioElements = await playwright.locator('audio, video').all();
      
      for (const audio of audioElements) {
        const autoplay = await audio.getAttribute('autoplay');
        const controls = await audio.getAttribute('controls');
        const muted = await audio.getAttribute('muted');
        
        // If autoplay is present, should have controls or be muted
        if (autoplay !== null) {
          expect(controls !== null || muted !== null).toBeTruthy();
        }
      }
    });
  });
}
