import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';
import { AccessibilityTester, generateAccessibilityReport } from '../utils/accessibility-utils';

/**
 * Comprehensive WCAG 2.2 Accessibility Test Suite
 * Combines axe-playwright with custom checks for complete coverage
 */

const pages = [
  { name: 'Home', url: '/' },
  { name: 'Pricing', url: '/pricing' },
  { name: 'Results', url: '/results' },
];

for (const page of pages) {
  test.describe(`Comprehensive WCAG 2.2 Tests - ${page.name} Page`, () => {
    
    test(`should pass all axe-core accessibility checks on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      const accessibilityScanResults = await new AxeBuilder({ page: playwright })
        .withTags(['wcag2a', 'wcag2aa', 'wcag21aa', 'wcag22aa'])
        .analyze();

      // Log detailed results for debugging
      if (accessibilityScanResults.violations.length > 0) {
        console.log(`\n❌ Axe violations found on ${page.name}:`);
        accessibilityScanResults.violations.forEach(violation => {
          console.log(`\n🔴 ${violation.id} (${violation.impact}): ${violation.description}`);
          console.log(`   Help: ${violation.helpUrl}`);
          violation.nodes.forEach(node => {
            console.log(`   Element: ${node.target.join(', ')}`);
            console.log(`   HTML: ${node.html}`);
          });
        });
      }

      expect(accessibilityScanResults.violations).toEqual([]);
    });

    test(`should pass all custom accessibility checks on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      const tester = new AccessibilityTester(playwright);
      const results = await tester.runAllChecks();
      
      // Generate and log comprehensive report
      const report = generateAccessibilityReport(results);
      console.log(report);
      
      // Check for failures
      const failures = results.filter(result => result.status === 'fail');
      
      if (failures.length > 0) {
        const failureMessages = failures.map(failure => 
          `${failure.rule}: ${failure.description} - ${failure.elements?.join(', ')}`
        );
        throw new Error(`Custom accessibility checks failed:\n${failureMessages.join('\n')}`);
      }
      
      expect(failures.length).toBe(0);
    });

    test(`should have proper color contrast ratios on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      // Test both minimum (AA) and enhanced (AAA) contrast
      const contrastResults = await new AxeBuilder({ page: playwright })
        .withRules(['color-contrast'])
        .analyze();

      expect(contrastResults.violations).toEqual([]);
    });

    test(`should be fully keyboard navigable on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      // Test keyboard navigation
      const keyboardResults = await new AxeBuilder({ page: playwright })
        .withRules(['keyboard', 'focus-order-semantics', 'tabindex'])
        .analyze();

      expect(keyboardResults.violations).toEqual([]);

      // Additional keyboard navigation test
      const focusableElements = await playwright.locator(
        'button, a[href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      ).all();

      if (focusableElements.length > 0) {
        // Test that we can navigate through all elements
        await focusableElements[0].focus();
        
        for (let i = 0; i < Math.min(focusableElements.length - 1, 10); i++) {
          await playwright.keyboard.press('Tab');
          
          const focusedElement = await playwright.locator(':focus').first();
          const isVisible = await focusedElement.isVisible();
          expect(isVisible).toBeTruthy();
        }
      }
    });

    test(`should have proper ARIA implementation on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      const ariaResults = await new AxeBuilder({ page: playwright })
        .withRules([
          'aria-roles',
          'aria-valid-attr',
          'aria-valid-attr-value',
          'aria-required-attr',
          'aria-required-children',
          'aria-required-parent'
        ])
        .analyze();

      expect(ariaResults.violations).toEqual([]);
    });

    test(`should have proper form accessibility on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      const formResults = await new AxeBuilder({ page: playwright })
        .withRules([
          'label',
          'form-field-multiple-labels',
          'autocomplete-valid',
          'duplicate-id'
        ])
        .analyze();

      expect(formResults.violations).toEqual([]);
    });

    test(`should have proper document structure on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      const structureResults = await new AxeBuilder({ page: playwright })
        .withRules([
          'document-title',
          'html-has-lang',
          'landmark-one-main',
          'page-has-heading-one',
          'region'
        ])
        .analyze();

      expect(structureResults.violations).toEqual([]);
    });

    test(`should handle responsive design properly on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      // Test multiple viewport sizes
      const viewports = [
        { width: 320, height: 568, name: 'Mobile Portrait' },
        { width: 768, height: 1024, name: 'Tablet Portrait' },
        { width: 1024, height: 768, name: 'Tablet Landscape' },
        { width: 1280, height: 720, name: 'Desktop' }
      ];

      for (const viewport of viewports) {
        await playwright.setViewportSize({ width: viewport.width, height: viewport.height });
        await playwright.waitForTimeout(500);

        // Check for horizontal scrolling
        const bodyScrollWidth = await playwright.evaluate(() => document.body.scrollWidth);
        const viewportWidth = await playwright.evaluate(() => window.innerWidth);
        
        expect(bodyScrollWidth).toBeLessThanOrEqual(viewportWidth + 10);

        // Run axe tests at this viewport
        const responsiveResults = await new AxeBuilder({ page: playwright })
          .withTags(['wcag2aa'])
          .analyze();

        if (responsiveResults.violations.length > 0) {
          console.log(`\n❌ Responsive violations at ${viewport.name} (${viewport.width}x${viewport.height}):`);
          responsiveResults.violations.forEach(violation => {
            console.log(`   ${violation.id}: ${violation.description}`);
          });
        }

        expect(responsiveResults.violations).toEqual([]);
      }
    });

    test(`should generate comprehensive accessibility report for ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      // Run comprehensive axe scan
      const axeResults = await new AxeBuilder({ page: playwright })
        .withTags(['wcag2a', 'wcag2aa', 'wcag21aa', 'wcag22aa'])
        .analyze();

      // Run custom checks
      const tester = new AccessibilityTester(playwright);
      const customResults = await tester.runAllChecks();

      // Generate comprehensive report
      console.log(`\n📊 COMPREHENSIVE ACCESSIBILITY REPORT - ${page.name.toUpperCase()}`);
      console.log('='.repeat(60));
      
      console.log(`\n🔍 AXE-CORE RESULTS:`);
      console.log(`   ✅ Passed: ${axeResults.passes.length} rules`);
      console.log(`   ❌ Failed: ${axeResults.violations.length} rules`);
      console.log(`   ⚠️  Incomplete: ${axeResults.incomplete.length} rules`);
      console.log(`   ➖ Not Applicable: ${axeResults.inapplicable.length} rules`);

      if (axeResults.violations.length > 0) {
        console.log(`\n❌ AXE VIOLATIONS:`);
        axeResults.violations.forEach(violation => {
          console.log(`   ${violation.id} (${violation.impact}): ${violation.description}`);
        });
      }

      // Custom results report
      const customReport = generateAccessibilityReport(customResults);
      console.log(`\n🔧 CUSTOM CHECKS:${customReport}`);

      // Overall assessment
      const totalViolations = axeResults.violations.length + 
                             customResults.filter(r => r.status === 'fail').length;
      
      console.log(`\n🎯 OVERALL ASSESSMENT:`);
      console.log(`   Total Violations: ${totalViolations}`);
      console.log(`   Compliance Level: ${totalViolations === 0 ? '✅ WCAG 2.2 AA Compliant' : '❌ Non-Compliant'}`);

      // Test should pass only if no violations
      expect(totalViolations).toBe(0);
    });
  });
}
