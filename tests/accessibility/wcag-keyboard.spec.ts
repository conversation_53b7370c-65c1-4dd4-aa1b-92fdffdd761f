import { test, expect } from '@playwright/test';

/**
 * WCAG 2.2 Keyboard Accessibility Tests
 * Tests keyboard navigation, focus management, and related accessibility features
 */

const pages = [
  { name: 'Home', url: '/' },
  { name: 'Pricing', url: '/pricing' },
  { name: 'Results', url: '/results' },
];

for (const page of pages) {
  test.describe(`WCAG 2.2 Keyboard Tests - ${page.name} Page`, () => {
    
    test(`should allow keyboard navigation through all interactive elements (2.1.1) on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      // Get all focusable elements
      const focusableElements = await playwright.locator(
        'button, a[href], input, select, textarea, [tabindex]:not([tabindex="-1"]), [role="button"], [role="link"]'
      ).all();
      
      if (focusableElements.length > 0) {
        // Start from the first focusable element
        await focusableElements[0].focus();
        
        // Tab through all elements
        for (let i = 0; i < focusableElements.length - 1; i++) {
          await playwright.keyboard.press('Tab');
          
          // Check that focus moved to the next element
          const focusedElement = await playwright.locator(':focus').first();
          const isVisible = await focusedElement.isVisible();
          expect(isVisible).toBeTruthy();
        }
      }
    });

    test(`should not create keyboard traps (2.1.2) on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      const focusableElements = await playwright.locator(
        'button, a[href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      ).all();
      
      if (focusableElements.length > 1) {
        // Focus on first element
        await focusableElements[0].focus();
        
        // Tab forward through several elements
        for (let i = 0; i < Math.min(5, focusableElements.length); i++) {
          await playwright.keyboard.press('Tab');
        }
        
        // Tab backward
        await playwright.keyboard.press('Shift+Tab');
        
        // Should be able to move focus backward
        const focusedElement = await playwright.locator(':focus').first();
        const isVisible = await focusedElement.isVisible();
        expect(isVisible).toBeTruthy();
      }
    });

    test(`should have visible focus indicators (2.4.7) on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      const focusableElements = await playwright.locator(
        'button, a[href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      ).all();
      
      for (const element of focusableElements) {
        const isVisible = await element.isVisible();
        if (isVisible) {
          await element.focus();
          
          // Check if element has focus styles
          const focusStyles = await element.evaluate(el => {
            const styles = window.getComputedStyle(el, ':focus');
            return {
              outline: styles.outline,
              outlineWidth: styles.outlineWidth,
              outlineStyle: styles.outlineStyle,
              outlineColor: styles.outlineColor,
              boxShadow: styles.boxShadow,
              border: styles.border
            };
          });
          
          // Element should have some form of focus indicator
          const hasFocusIndicator = 
            focusStyles.outline !== 'none' ||
            focusStyles.outlineWidth !== '0px' ||
            focusStyles.boxShadow !== 'none' ||
            focusStyles.border !== 'none';
          
          expect(hasFocusIndicator).toBeTruthy();
        }
      }
    });

    test(`should support keyboard shortcuts properly (2.1.4) on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      // Check for any keyboard event listeners
      const elementsWithKeyListeners = await playwright.evaluate(() => {
        const elements = document.querySelectorAll('*');
        const elementsWithListeners = [];
        
        elements.forEach(el => {
          const events = getEventListeners ? getEventListeners(el) : {};
          if (events.keydown || events.keyup || events.keypress) {
            elementsWithListeners.push({
              tagName: el.tagName,
              id: el.id,
              className: el.className
            });
          }
        });
        
        return elementsWithListeners;
      });
      
      // If keyboard shortcuts exist, they should be documented or configurable
      // This is a basic check - in practice, you'd need to verify specific shortcuts
      if (elementsWithKeyListeners.length > 0) {
        // Check if there's any documentation or settings for shortcuts
        const shortcutDocs = await playwright.locator(
          '[aria-label*="shortcut"], [title*="shortcut"], [aria-describedby*="shortcut"]'
        ).count();
        
        // This is a placeholder - actual implementation would depend on your app's shortcut system
        expect(true).toBeTruthy(); // Placeholder assertion
      }
    });

    test(`should maintain logical focus order (2.4.3) on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      const focusableElements = await playwright.locator(
        'button, a[href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      ).all();
      
      if (focusableElements.length > 1) {
        const focusOrder = [];
        
        // Record the visual order of focusable elements
        for (const element of focusableElements) {
          const isVisible = await element.isVisible();
          if (isVisible) {
            const boundingBox = await element.boundingBox();
            if (boundingBox) {
              focusOrder.push({
                element,
                top: boundingBox.y,
                left: boundingBox.x
              });
            }
          }
        }
        
        // Sort by visual position (top to bottom, left to right)
        focusOrder.sort((a, b) => {
          if (Math.abs(a.top - b.top) < 10) { // Same row
            return a.left - b.left;
          }
          return a.top - b.top;
        });
        
        // Test that tab order follows visual order
        if (focusOrder.length > 0) {
          await focusOrder[0].element.focus();
          
          for (let i = 1; i < Math.min(focusOrder.length, 5); i++) {
            await playwright.keyboard.press('Tab');
            
            const currentFocused = await playwright.locator(':focus').first();
            const currentBox = await currentFocused.boundingBox();
            
            // Focus should generally move in reading order
            expect(currentBox).toBeTruthy();
          }
        }
      }
    });

    test(`should not obscure focused elements (2.4.11) on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      const focusableElements = await playwright.locator(
        'button, a[href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      ).all();
      
      for (const element of focusableElements) {
        const isVisible = await element.isVisible();
        if (isVisible) {
          await element.focus();
          
          // Check if the focused element is visible in viewport
          const boundingBox = await element.boundingBox();
          const viewport = await playwright.viewportSize();
          
          if (boundingBox && viewport) {
            // Element should be at least partially visible
            const isInViewport = 
              boundingBox.x < viewport.width &&
              boundingBox.y < viewport.height &&
              boundingBox.x + boundingBox.width > 0 &&
              boundingBox.y + boundingBox.height > 0;
            
            expect(isInViewport).toBeTruthy();
          }
        }
      }
    });

    test(`should support pointer gesture alternatives (2.5.1) on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      // Check for elements that might use complex gestures
      const interactiveElements = await playwright.locator(
        '[onmousedown], [onmouseup], [onmousemove], [ontouchstart], [ontouchmove], [ontouchend]'
      ).all();
      
      for (const element of interactiveElements) {
        const isVisible = await element.isVisible();
        if (isVisible) {
          // Element should also be keyboard accessible or have single-pointer alternative
          const isKeyboardAccessible = await element.evaluate(el => {
            return el.tabIndex >= 0 || 
                   ['button', 'a', 'input', 'select', 'textarea'].includes(el.tagName.toLowerCase()) ||
                   el.getAttribute('role') === 'button';
          });
          
          expect(isKeyboardAccessible).toBeTruthy();
        }
      }
    });

    test(`should allow pointer cancellation (2.5.2) on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      const buttons = await playwright.locator('button, [role="button"]').all();
      
      for (const button of buttons) {
        const isVisible = await button.isVisible();
        if (isVisible) {
          // Check if button uses mouseup instead of mousedown for activation
          const hasMouseUpHandler = await button.evaluate(el => {
            return el.onmouseup !== null || el.onclick !== null;
          });
          
          const hasMouseDownHandler = await button.evaluate(el => {
            return el.onmousedown !== null;
          });
          
          // Buttons should preferably use mouseup/click rather than mousedown
          if (hasMouseDownHandler) {
            expect(hasMouseUpHandler).toBeTruthy();
          }
        }
      }
    });
  });
}
