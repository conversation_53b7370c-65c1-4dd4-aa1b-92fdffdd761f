import { test, expect } from '@playwright/test';

/**
 * WCAG 2.2 Responsive and Reflow Tests
 * Tests rules related to responsive design and content reflow
 */

const pages = [
  { name: 'Home', url: '/' },
  { name: 'Pricing', url: '/pricing' },
  { name: 'Results', url: '/results' },
];

for (const page of pages) {
  test.describe(`WCAG 2.2 Responsive Tests - ${page.name} Page`, () => {
    
    test(`should reflow content properly at 320px width (1.4.10) on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      // Set viewport to 320px width (equivalent to 1280px at 400% zoom)
      await playwright.setViewportSize({ width: 320, height: 568 });
      
      // Wait for any responsive changes
      await playwright.waitForTimeout(1000);
      
      // Check for horizontal scrolling
      const bodyScrollWidth = await playwright.evaluate(() => document.body.scrollWidth);
      const viewportWidth = await playwright.evaluate(() => window.innerWidth);
      
      // Content should not require horizontal scrolling
      expect(bodyScrollWidth).toBeLessThanOrEqual(viewportWidth + 10); // Allow small tolerance
    });

    test(`should maintain functionality at 200% zoom (1.4.4) on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      // Simulate 200% zoom by setting smaller viewport
      const originalWidth = 1280;
      const originalHeight = 720;
      await playwright.setViewportSize({ 
        width: Math.floor(originalWidth / 2), 
        height: Math.floor(originalHeight / 2) 
      });
      
      // Wait for responsive changes
      await playwright.waitForTimeout(1000);
      
      // Check that interactive elements are still accessible
      const buttons = await playwright.locator('button, a, input[type="submit"]').all();
      
      for (const button of buttons) {
        const isVisible = await button.isVisible();
        if (isVisible) {
          const boundingBox = await button.boundingBox();
          expect(boundingBox).toBeTruthy();
          
          // Element should be reasonably sized and positioned
          if (boundingBox) {
            expect(boundingBox.width).toBeGreaterThan(0);
            expect(boundingBox.height).toBeGreaterThan(0);
          }
        }
      }
    });

    test(`should have minimum target sizes (2.5.8) on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      // Check interactive elements have minimum 24x24px size
      const interactiveElements = await playwright.locator(
        'button, a, input[type="submit"], input[type="button"], [role="button"], [tabindex="0"]'
      ).all();
      
      for (const element of interactiveElements) {
        const isVisible = await element.isVisible();
        if (isVisible) {
          const boundingBox = await element.boundingBox();
          
          if (boundingBox) {
            // WCAG 2.2 Level AA requires minimum 24x24 CSS pixels
            expect(boundingBox.width).toBeGreaterThanOrEqual(24);
            expect(boundingBox.height).toBeGreaterThanOrEqual(24);
          }
        }
      }
    });

    test(`should handle text spacing adjustments (1.4.12) on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      // Apply WCAG text spacing requirements
      await playwright.addStyleTag({
        content: `
          * {
            line-height: 1.5 !important;
            letter-spacing: 0.12em !important;
            word-spacing: 0.16em !important;
          }
          p {
            margin-bottom: 2em !important;
          }
        `
      });
      
      // Wait for styles to apply
      await playwright.waitForTimeout(1000);
      
      // Check that content is still readable and no text is cut off
      const textElements = await playwright.locator('p, h1, h2, h3, h4, h5, h6, span, div').all();
      
      for (const element of textElements) {
        const isVisible = await element.isVisible();
        if (isVisible) {
          const text = await element.textContent();
          if (text && text.trim().length > 0) {
            const boundingBox = await element.boundingBox();
            expect(boundingBox).toBeTruthy();
            
            // Element should still be visible and have reasonable dimensions
            if (boundingBox) {
              expect(boundingBox.width).toBeGreaterThan(0);
              expect(boundingBox.height).toBeGreaterThan(0);
            }
          }
        }
      }
    });

    test(`should support both portrait and landscape orientations (1.3.4) on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      // Test portrait orientation (mobile)
      await playwright.setViewportSize({ width: 375, height: 667 });
      await playwright.waitForTimeout(1000);
      
      let portraitScrollWidth = await playwright.evaluate(() => document.body.scrollWidth);
      let portraitViewportWidth = await playwright.evaluate(() => window.innerWidth);
      
      // Test landscape orientation (mobile)
      await playwright.setViewportSize({ width: 667, height: 375 });
      await playwright.waitForTimeout(1000);
      
      let landscapeScrollWidth = await playwright.evaluate(() => document.body.scrollWidth);
      let landscapeViewportWidth = await playwright.evaluate(() => window.innerWidth);
      
      // Content should work in both orientations without horizontal scrolling
      expect(portraitScrollWidth).toBeLessThanOrEqual(portraitViewportWidth + 10);
      expect(landscapeScrollWidth).toBeLessThanOrEqual(landscapeViewportWidth + 10);
    });

    test(`should have enhanced target sizes for AAA compliance (2.5.5) on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      // Check interactive elements have minimum 44x44px size for AAA
      const interactiveElements = await playwright.locator(
        'button, a, input[type="submit"], input[type="button"], [role="button"]'
      ).all();
      
      for (const element of interactiveElements) {
        const isVisible = await element.isVisible();
        if (isVisible) {
          const boundingBox = await element.boundingBox();
          
          if (boundingBox) {
            // WCAG 2.2 Level AAA requires minimum 44x44 CSS pixels
            expect(boundingBox.width).toBeGreaterThanOrEqual(44);
            expect(boundingBox.height).toBeGreaterThanOrEqual(44);
          }
        }
      }
    });

    test(`should not restrict input mechanisms (2.5.6) on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      // Check that interactive elements support multiple input methods
      const interactiveElements = await playwright.locator(
        'button, a, input, select, textarea, [role="button"], [tabindex="0"]'
      ).all();
      
      for (const element of interactiveElements) {
        const isVisible = await element.isVisible();
        if (isVisible) {
          // Element should be focusable (keyboard accessible)
          const isFocusable = await element.evaluate(el => {
            const tabIndex = el.getAttribute('tabindex');
            return el.tabIndex >= 0 || tabIndex === '0' || 
                   ['button', 'a', 'input', 'select', 'textarea'].includes(el.tagName.toLowerCase());
          });
          
          expect(isFocusable).toBeTruthy();
        }
      }
    });
  });
}
