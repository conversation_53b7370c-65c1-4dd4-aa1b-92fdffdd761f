import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';

/**
 * Base WCAG 2.2 Accessibility Tests
 * Tests all rules that are detectable by axe-playwright
 */

const pages = [
  { name: 'Home', url: '/' },
  { name: 'Pricing', url: '/pricing' },
  { name: 'Results', url: '/results' },
];

// Test each page for axe-detectable WCAG violations
for (const page of pages) {
  test.describe(`WCAG 2.2 Compliance - ${page.name} Page`, () => {
    
    test(`should not have any axe-detectable accessibility violations on ${page.name}`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      const accessibilityScanResults = await new AxeBuilder({ page: playwright })
        .withTags(['wcag2a', 'wcag2aa', 'wcag21aa', 'wcag22aa'])
        .analyze();

      expect(accessibilityScanResults.violations).toEqual([]);
    });

    test(`should have proper color contrast on ${page.name} (1.4.3)`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      const accessibilityScanResults = await new AxeBuilder({ page: playwright })
        .withRules(['color-contrast'])
        .analyze();

      expect(accessibilityScanResults.violations).toEqual([]);
    });

    test(`should be keyboard accessible on ${page.name} (2.1.1)`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      const accessibilityScanResults = await new AxeBuilder({ page: playwright })
        .withRules(['keyboard', 'focus-order-semantics'])
        .analyze();

      expect(accessibilityScanResults.violations).toEqual([]);
    });

    test(`should not have keyboard traps on ${page.name} (2.1.2)`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      const accessibilityScanResults = await new AxeBuilder({ page: playwright })
        .withRules(['no-keyboard-trap'])
        .analyze();

      expect(accessibilityScanResults.violations).toEqual([]);
    });

    test(`should have visible focus indicators on ${page.name} (2.4.7)`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      const accessibilityScanResults = await new AxeBuilder({ page: playwright })
        .withRules(['focus-order-semantics'])
        .analyze();

      expect(accessibilityScanResults.violations).toEqual([]);
    });

    test(`should have proper ARIA attributes on ${page.name} (4.1.2)`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      const accessibilityScanResults = await new AxeBuilder({ page: playwright })
        .withRules(['aria-roles', 'aria-valid-attr', 'aria-valid-attr-value'])
        .analyze();

      expect(accessibilityScanResults.violations).toEqual([]);
    });

    test(`should have proper language declaration on ${page.name} (3.1.1)`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      const accessibilityScanResults = await new AxeBuilder({ page: playwright })
        .withRules(['html-has-lang'])
        .analyze();

      expect(accessibilityScanResults.violations).toEqual([]);
    });

    test(`should have proper form labels on ${page.name} (3.3.2)`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      const accessibilityScanResults = await new AxeBuilder({ page: playwright })
        .withRules(['label', 'form-field-multiple-labels'])
        .analyze();

      expect(accessibilityScanResults.violations).toEqual([]);
    });

    test(`should have proper status messages on ${page.name} (4.1.3)`, async ({ page: playwright }) => {
      await playwright.goto(page.url);
      
      const accessibilityScanResults = await new AxeBuilder({ page: playwright })
        .withRules(['aria-live'])
        .analyze();

      expect(accessibilityScanResults.violations).toEqual([]);
    });
  });
}
