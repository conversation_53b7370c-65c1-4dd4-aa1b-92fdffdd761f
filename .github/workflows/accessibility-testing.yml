name: WCAG 2.2 Accessibility Testing

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run accessibility tests daily at 2 AM UTC
    - cron: '0 2 * * *'

jobs:
  accessibility-tests:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - uses: actions/setup-node@v4
      with:
        node-version: 18
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Install Playwright Browsers
      run: npx playwright install --with-deps
    
    - name: Build application
      run: npm run build
    
    - name: Start application
      run: npm start &
      env:
        NODE_ENV: production
    
    - name: Wait for application to be ready
      run: npx wait-on http://localhost:3000 --timeout 60000
    
    - name: Run WCAG 2.2 Accessibility Tests
      run: npm run test:accessibility
      env:
        CI: true
    
    - name: Upload Playwright Report
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: accessibility-report
        path: playwright-report/
        retention-days: 30
    
    - name: Upload Test Results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: accessibility-test-results
        path: test-results/
        retention-days: 30

  axe-only-tests:
    timeout-minutes: 30
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - uses: actions/setup-node@v4
      with:
        node-version: 18
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Install Playwright Browsers
      run: npx playwright install --with-deps
    
    - name: Build application
      run: npm run build
    
    - name: Start application
      run: npm start &
      env:
        NODE_ENV: production
    
    - name: Wait for application to be ready
      run: npx wait-on http://localhost:3000 --timeout 60000
    
    - name: Run Axe-Core Tests Only
      run: npm run test:accessibility:axe
      env:
        CI: true

  custom-checks:
    timeout-minutes: 30
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - uses: actions/setup-node@v4
      with:
        node-version: 18
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Install Playwright Browsers
      run: npx playwright install --with-deps
    
    - name: Build application
      run: npm run build
    
    - name: Start application
      run: npm start &
      env:
        NODE_ENV: production
    
    - name: Wait for application to be ready
      run: npx wait-on http://localhost:3000 --timeout 60000
    
    - name: Run Custom Accessibility Checks
      run: npm run test:accessibility:custom
      env:
        CI: true

  keyboard-navigation:
    timeout-minutes: 30
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - uses: actions/setup-node@v4
      with:
        node-version: 18
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Install Playwright Browsers
      run: npx playwright install --with-deps
    
    - name: Build application
      run: npm run build
    
    - name: Start application
      run: npm start &
      env:
        NODE_ENV: production
    
    - name: Wait for application to be ready
      run: npx wait-on http://localhost:3000 --timeout 60000
    
    - name: Run Keyboard Navigation Tests
      run: npm run test:accessibility:keyboard
      env:
        CI: true

  responsive-accessibility:
    timeout-minutes: 30
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - uses: actions/setup-node@v4
      with:
        node-version: 18
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Install Playwright Browsers
      run: npx playwright install --with-deps
    
    - name: Build application
      run: npm run build
    
    - name: Start application
      run: npm start &
      env:
        NODE_ENV: production
    
    - name: Wait for application to be ready
      run: npx wait-on http://localhost:3000 --timeout 60000
    
    - name: Run Responsive Accessibility Tests
      run: npm run test:accessibility:responsive
      env:
        CI: true

  manual-checklist:
    timeout-minutes: 15
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - uses: actions/setup-node@v4
      with:
        node-version: 18
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Install Playwright Browsers
      run: npx playwright install --with-deps
    
    - name: Build application
      run: npm run build
    
    - name: Start application
      run: npm start &
      env:
        NODE_ENV: production
    
    - name: Wait for application to be ready
      run: npx wait-on http://localhost:3000 --timeout 60000
    
    - name: Generate Manual Testing Checklist
      run: npm run test:accessibility:manual
      env:
        CI: true

  comprehensive-report:
    timeout-minutes: 45
    runs-on: ubuntu-latest
    needs: [accessibility-tests, axe-only-tests, custom-checks, keyboard-navigation, responsive-accessibility]
    if: always()
    
    steps:
    - uses: actions/checkout@v4
    
    - uses: actions/setup-node@v4
      with:
        node-version: 18
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Install Playwright Browsers
      run: npx playwright install --with-deps
    
    - name: Build application
      run: npm run build
    
    - name: Start application
      run: npm start &
      env:
        NODE_ENV: production
    
    - name: Wait for application to be ready
      run: npx wait-on http://localhost:3000 --timeout 60000
    
    - name: Generate Comprehensive Accessibility Report
      run: npx playwright test tests/accessibility/wcag-comprehensive.spec.ts --reporter=html
      env:
        CI: true
    
    - name: Upload Comprehensive Report
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: comprehensive-accessibility-report
        path: playwright-report/
        retention-days: 30
