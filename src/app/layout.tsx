import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Samya | Accessibility Audit Tool",
  description: "End-to-end accessibility solution for websites and mobile apps. Get a free WCAG 2.2 compliance audit report instantly.",
  keywords: ["accessibility", "WCAG", "website audit", "ADA compliance", "accessibility testing", "web accessibility"],
  authors: [{ name: "Samya Team" }],
  creator: "<PERSON><PERSON>",
  publisher: "<PERSON><PERSON>",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://samya.app",
    title: "Samya | Accessibility Audit Tool",
    description: "End-to-end accessibility solution for websites and mobile apps. Get a free WCAG 2.2 compliance audit report instantly.",
    siteName: "Samya",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "Samya - Website Accessibility Audit Tool",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Samya | Website Accessibility Audit Tool",
    description: "End-to-end accessibility solution for websites and mobile apps. Get a free WCAG 2.2 compliance audit report instantly.",
    images: ["/twitter-image.png"],
    creator: "@samyaapp",
  },
  alternates: {
    canonical: "https://samya.app",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
