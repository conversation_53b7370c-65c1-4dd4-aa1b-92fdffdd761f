'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';

interface AxeViolation {
  id: string;
  impact: string;
  help: string;
  helpUrl: string;
  nodes: Array<{
    html: string;
    target: string[];
    failureSummary?: string;
  }>;
}
interface ScanResult {
  url: string;
  success: boolean;
  total_issues: number;
  issues: Array<{
    id: string;
    impact: string;
    help: string;
    helpUrl: string;
    nodes: number;
  }>;
  raw_results: AxeViolation[];
}

export default function Home() {
  const [url, setUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [scanResult, setScanResult] = useState<ScanResult | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted');
    
    // Reset states
    setError('');
    setScanResult(null);
    
    // Validate URL
    if (!url) {
      setError('Please enter a website URL');
      console.log('Error: Empty URL');
      return;
    }
    
    // Add protocol if missing
    let formattedUrl = url;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      formattedUrl = `https://${url}`;
      console.log('Added protocol, formatted URL:', formattedUrl);
    }
    
    try {
      // Validate URL format
      new URL(formattedUrl);
      console.log('URL is valid');
    } catch (error) {
      setError('Please enter a valid URL');
      console.log('Error: Invalid URL format');
      return;
    }
    
    // Start loading
    setIsLoading(true);
    console.log('Loading started, sending request to /api/scan');
    
    try {
      console.log('Fetching with URL:', formattedUrl);
      const response = await fetch('/api/scan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url: formattedUrl }),
      });
      
      console.log('Response received:', response.status);
      const data = await response.json();
      console.log('Response data:', data);
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to scan website');
      }
      
      // Store scan result in session storage for the results page
      sessionStorage.setItem('scanResult', JSON.stringify(data));
      
      // Redirect to results page
      window.location.href = `/results?url=${encodeURIComponent(formattedUrl)}`;
      
    } catch (error) {
      console.error('Error during fetch:', error);
      setError((error as Error).message || 'An error occurred while scanning the website');
    } finally {
      setIsLoading(false);
      console.log('Loading finished');
    }
  };

  useEffect(() => {
    if (scanResult && scanResult.url) {
      // Store scan result in session storage for the results page
      sessionStorage.setItem('scanResult', JSON.stringify(scanResult));
      // Redirect to results page
      window.location.href = `/results?url=${encodeURIComponent(scanResult.url)}`;
    }
  }, [scanResult]);

  return (
    <div className="flex flex-col min-h-screen bg-[#1a1f2e] text-white">
      {/* Header */}
      <header className="py-4 px-8 flex justify-between items-center">
        <div className="flex items-center">
          <Image 
            src="/samya.png" 
            alt="Samya Logo" 
            width={40} 
            height={40} 
            className="mr-3"
          />
        </div>
        <nav className="flex items-center space-x-4">
          <Link href="/pricing" className="text-gray-300 hover:text-white">
            Pricing
          </Link>
          <Link
            href="#"
            className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
          >
            Login
          </Link>
        </nav>
      </header>

      {/* Hero Section */}
      <main className="flex-grow flex flex-col items-center justify-center text-center px-4 py-12">
        <h1 className="text-5xl font-bold mb-2">Samya</h1>
        <p className="text-lg text-gray-300 mb-8 max-w-2xl">
          End to end accessibility solution for websites and mobile apps
        </p>
        <form onSubmit={handleSubmit} className="w-full max-w-xl flex flex-col items-center">
          <div className="relative flex-grow w-full mb-4">
            <div className="flex items-center text-gray-400 text-sm mb-2">
              <span className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                </svg>
                Enter Website URL
              </span>
            </div>
            <div className="flex">
              <div className="bg-[#252a3a] border-y border-l border-gray-700 rounded-l-md px-3 py-3 text-gray-400">
                https://
              </div>
              <input
                type="text"
                placeholder="example.com"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                className="w-full px-4 py-3 bg-[#252a3a] border border-gray-700 rounded-r-md focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-white"
                disabled={isLoading}
              />
            </div>
            {error && <p className="text-red-500 text-sm mt-2">{error}</p>}
          </div>
          
          {!isLoading ? (
            <button 
              type="submit"
              className="bg-indigo-600 text-white px-8 py-3 rounded-md hover:bg-indigo-700 w-full"
            >
              Free Audit Report
            </button>
          ) : (
            <div className="bg-indigo-600 text-white px-8 py-3 rounded-md w-full flex items-center justify-center space-x-2">
              <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span>Analyzing Website...</span>
            </div>
          )}
          
          <div className="mt-6 flex items-center justify-center">
            <span className="flex items-center text-gray-400">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
              Mobile Accessibility Solution
            </span>
          </div>
        </form>
        
      </main>

      <section className="py-12 px-4 bg-[#1e2335]">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-2xl font-bold text-center mb-8">
            What We Check
          </h2>
          <div className="grid md:grid-cols-2 gap-x-12 gap-y-4">
            <div className="flex items-start">
              <span className="text-green-500 text-lg mr-3">✓</span>
              <p className="text-gray-300">WCAG 2.2 Guidelines Compliance</p>
            </div>
            <div className="flex items-start">
              <span className="text-green-500 text-lg mr-3">✓</span>
              <p className="text-gray-300">Accessibility Structure & Navigation</p>
            </div>
            <div className="flex items-start">
              <span className="text-green-500 text-lg mr-3">✓</span>
              <p className="text-gray-300">Color Contrast & Visual Elements</p>
            </div>
            <div className="flex items-start">
              <span className="text-green-500 text-lg mr-3">✓</span>
              <p className="text-gray-300">Interactive Elements & Forms</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
} 
