import React from 'react';
import Image from 'next/image';
import Link from 'next/link';

export default function Pricing() {
  return (
    <div className="flex flex-col min-h-screen bg-[#1a1f2e] text-white">
      {/* Header */}
      <header className="py-4 px-8 flex justify-between items-center">
        <div className="flex items-center">
          <Link href="/">
            <Image 
              src="/samya.png" 
              alt="Samya Logo" 
              width={40} 
              height={40} 
              className="mr-3"
            />
          </Link>
        </div>
        <nav className="flex items-center space-x-4">
          <Link href="/pricing" className="text-gray-300 hover:text-white">
            Pricing
          </Link>
          <Link
            href="#"
            className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
          >
            Login
          </Link>
        </nav>
      </header>

      {/* Pricing Section */}
      <main className="flex-grow py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-4xl font-bold text-center mb-12">Comprehensive Audit & Recommendations</h1>
          
          <div className="grid md:grid-cols-3 gap-8 mb-8">
            {/* Small Website */}
            <div className="bg-[#252a3a] rounded-lg p-8 border border-gray-700 flex flex-col">
              <h2 className="text-xl font-bold mb-4">Small Website</h2>
              <div className="text-3xl font-bold mb-4">$200</div>
              <p className="text-gray-300 mb-6">For websites with less than 5 pages</p>
              <ul className="space-y-3 mb-8 flex-grow">
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span className="text-gray-300">Full WCAG 2.2 Audit</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span className="text-gray-300">Detailed PDF Report</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span className="text-gray-300">Actionable Recommendations</span>
                </li>
              </ul>
              <button className="w-full py-3 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                Get Started
              </button>
            </div>

            {/* Medium Website */}
            <div className="bg-[#252a3a] rounded-lg p-8 border border-indigo-500 flex flex-col">
              <h2 className="text-xl font-bold mb-4">Medium Website</h2>
              <div className="text-3xl font-bold mb-4">$300</div>
              <p className="text-gray-300 mb-6">For websites with 5-10 pages</p>
              <ul className="space-y-3 mb-8 flex-grow">
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span className="text-gray-300">Full WCAG 2.2 Audit</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span className="text-gray-300">Detailed PDF Report</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span className="text-gray-300">Actionable Recommendations</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span className="text-gray-300">Priority Fixes Guide</span>
                </li>
              </ul>
              <button className="w-full py-3 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                Get Started
              </button>
            </div>

            {/* Large Website */}
            <div className="bg-[#252a3a] rounded-lg p-8 border border-gray-700 flex flex-col">
              <h2 className="text-xl font-bold mb-4">Large Website</h2>
              <div className="text-3xl font-bold mb-4">Request</div>
              <p className="text-gray-300 mb-6">For websites with more than 10 pages</p>
              <ul className="space-y-3 mb-8 flex-grow">
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span className="text-gray-300">Custom Audit Plan</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span className="text-gray-300">Comprehensive Report</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span className="text-gray-300">Implementation Support</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span className="text-gray-300">Ongoing Monitoring</span>
                </li>
              </ul>
              <button className="w-full py-3 bg-gray-600 text-white rounded-md hover:bg-gray-700">
                Request
              </button>
            </div>
          </div>

          {/* Enterprise Subscription */}
          <div className="bg-[#252a3a] rounded-lg p-8 border border-gray-700 mb-12">
            <h2 className="text-xl font-bold mb-4">Enterprise Subscription</h2>
            <p className="text-gray-300 mb-6">Contact us for custom enterprise solutions including:</p>
            <div className="grid md:grid-cols-2 gap-4 mb-8">
              <div className="flex items-start">
                <span className="text-green-500 mr-2">✓</span>
                <span className="text-gray-300">Continuous Accessibility Monitoring</span>
              </div>
              <div className="flex items-start">
                <span className="text-green-500 mr-2">✓</span>
                <span className="text-gray-300">Developer Training & Support</span>
              </div>
              <div className="flex items-start">
                <span className="text-green-500 mr-2">✓</span>
                <span className="text-gray-300">Custom Integration Options</span>
              </div>
              <div className="flex items-start">
                <span className="text-green-500 mr-2">✓</span>
                <span className="text-gray-300">Priority Support Channel</span>
              </div>
            </div>
            <div className="flex justify-center">
              <button className="px-8 py-3 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                Request
              </button>
            </div>
          </div>

          <div className="flex justify-center">
            <Link href="/" className="px-6 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-600">
              Back to Audit Test
            </Link>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="py-8 px-8 text-center text-gray-400 border-t border-gray-800">
        <p>&copy; {new Date().getFullYear()} Samya. All rights reserved.</p>
      </footer>
    </div>
  );
}
