import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json();
    
    if (!url) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      );
    }
    
    // Validate URL format
    try {
      new URL(url);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid URL format' },
        { status: 400 }
      );
    }
    
    // Call the axe-playwright service
    const axePlaywrightEndpoint = 'https://axe-playwright-fly.fly.dev/scan';
    
    // Get token from environment variable (will need to be set in production)
    const token = process.env.AXE_PLAYWRIGHT_TOKEN;

    console.log("--------------------------------")
    console.log(token);
    console.log("--------------------------------")
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    
    // Add authorization header if token exists
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    const response = await fetch(axePlaywrightEndpoint, {
      method: 'POST',
      headers,
      body: JSON.stringify({ url }),
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return NextResponse.json(
        { 
          error: 'Scan failed', 
          details: errorData.error || response.statusText,
          status: response.status 
        },
        { status: response.status }
      );
    }
    
    const data = await response.json();
    
    // Transform the data to match our expected format
    // This is a simplified version of the Python transform_axe_results function
    const transformedData = {
      url,
      success: true,
      total_issues: data.length,
      issues: data.map((violation: any) => ({
        id: violation.id,
        impact: violation.impact,
        help: violation.help,
        helpUrl: violation.helpUrl,
        nodes: violation.nodes.length,
      })),
      raw_results: data
    };
    
    return NextResponse.json(transformedData);
  } catch (error) {
    console.error('Error scanning URL:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: (error as Error).message },
      { status: 500 }
    );
  }
}