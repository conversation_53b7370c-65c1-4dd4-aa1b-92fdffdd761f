'use client';

import React, { useEffect, useState, Suspense } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';

interface Issue {
  id: string;
  impact: string;
  help: string;
  helpUrl: string;
  nodes: number;
  element?: string;
  contrastRatio?: number;
  expectedRatio?: number;
  foregroundColor?: string;
  backgroundColor?: string;
  fontSize?: string;
  fontWeight?: string;
}

interface ScanResult {
  url: string;
  total_issues: number;
  issues: Issue[];
  pages_analyzed?: number;
  raw_results: any[];
}

function ResultsContent() {
  const searchParams = useSearchParams();
  const [scanResult, setScanResult] = useState<ScanResult | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get URL from query params or session storage
    const urlParam = searchParams.get('url');
    
    if (urlParam) {
      // Fetch results for the URL
      fetchResults(urlParam);
    } else {
      // Try to get from session storage
      const storedResult = sessionStorage.getItem('scanResult');
      if (storedResult) {
        setScanResult(JSON.parse(storedResult));
      }
      setLoading(false);
    }
  }, [searchParams]);

  const fetchResults = async (url: string) => {
    try {
      setLoading(true);
      const response = await fetch('/api/scan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to scan website');
      }
      
      // Add mock data for the demo
      const enhancedData = {
        ...data,
        pages_analyzed: 1,
        issues: data.issues.map((issue: Issue) => ({
          ...issue,
          element: issue.impact === 'critical' ? 
            'a[data-analytics-title="buy - ipad air"]' : 
            '.fam-gallery-item[data-gallery-id="1647864828"][data-analytics-gallery-item-id="fam-gallery-item-1"]',
          contrastRatio: 4.24,
          expectedRatio: 4.5,
          foregroundColor: '#0066cc',
          backgroundColor: '#2f3f1f',
          fontSize: '10.5pt (14px)',
          fontWeight: 'normal'
        }))
      };
      
      setScanResult(enhancedData);
      // Store in session storage
      sessionStorage.setItem('scanResult', JSON.stringify(enhancedData));
    } catch (error) {
      console.error('Error fetching results:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#1a1f2e] text-white">
      {/* Header */}
      <header className="py-4 px-8 flex justify-between items-center">
        <div className="flex items-center">
          <Link href="/">
            <Image 
              src="/samya.png" 
              alt="Samya Logo" 
              width={40} 
              height={40} 
              className="mr-3"
            />
          </Link>
        </div>
        <nav className="flex items-center space-x-4">
          <Link href="/pricing" className="text-gray-300 hover:text-white">
            Pricing
          </Link>
          <Link
            href="#"
            className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
          >
            Login
          </Link>
        </nav>
      </header>

      {/* Results Content */}
      <main className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-6">Free Accessibility Audit Results</h1>
        
        {loading ? (
          <div className="flex justify-center items-center py-20">
            <svg className="animate-spin h-10 w-10 text-indigo-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
        ) : scanResult ? (
          <>
            <div className="bg-[#252a3a] p-4 rounded-md mb-4">
              <p className="text-gray-300">{scanResult.url}</p>
            </div>
            
            <Link href="/pricing" className="text-indigo-400 hover:text-indigo-300 inline-flex items-center mb-8">
              Pricing for comprehensive audit and solutions →
            </Link>
            
            {/* Summary Section */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold mb-4">Summary</h2>
              <div className="flex justify-between items-start">
                <div className="grid grid-cols-2 gap-x-12 gap-y-2">
                  <div>
                    <p className="text-gray-400">Total Issues</p>
                    <p className="text-2xl font-bold">{scanResult.total_issues}</p>
                  </div>
                  <div>
                    <p className="text-gray-400">Pages Analyzed</p>
                    <p className="text-2xl font-bold">{scanResult.pages_analyzed || 1}</p>
                  </div>
                </div>
                
                <button className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Download PDF Report
                </button>
              </div>
            </div>
            
            {/* Critical Issues Section */}
            {scanResult.issues.filter(issue => issue.impact === 'critical').length > 0 && (
              <div className="mb-8">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold">Critical Issues</h2>
                  <span className="bg-red-500 text-white text-xs px-2 py-1 rounded">
                    {scanResult.issues.filter(issue => issue.impact === 'critical').length} Critical
                  </span>
                </div>
                
                {scanResult.issues.filter(issue => issue.impact === 'critical').map((issue, index) => (
                  <div key={index} className="bg-[#2d3548] rounded-md mb-4 overflow-hidden">
                    <div className="bg-[#3a4257] p-3">
                      <div className="flex items-center">
                        <span className="bg-red-500 text-white text-xs px-2 py-1 rounded mr-3">Critical</span>
                        <span className="text-gray-300 text-sm">Element: Element</span>
                      </div>
                      <p className="mt-2">Elements must meet minimum color contrast ratio thresholds</p>
                      <p className="text-gray-400 text-sm mt-1">Affected element: <code className="bg-[#1a1f2e] px-1 py-0.5 rounded text-xs">{issue.element}</code></p>
                    </div>
                    
                    <div className="p-4 bg-[#252a3a] border-t border-gray-700">
                      <h3 className="font-semibold mb-2">Color Contrast Details:</h3>
                      <div className="space-y-1 text-sm">
                        <p>Contrast Ratio: <span className="font-mono">{issue.contrastRatio}</span> <span className="text-gray-400">(Expected: {issue.expectedRatio}:1)</span></p>
                        <div className="flex items-center">
                          <p className="mr-2">Foreground Color:</p>
                          <div className="w-4 h-4 inline-block mr-1" style={{backgroundColor: issue.foregroundColor}}></div>
                          <span className="font-mono">{issue.foregroundColor}</span>
                        </div>
                        <div className="flex items-center">
                          <p className="mr-2">Background Color:</p>
                          <div className="w-4 h-4 inline-block mr-1" style={{backgroundColor: issue.backgroundColor}}></div>
                          <span className="font-mono">{issue.backgroundColor}</span>
                        </div>
                        <p>Font Size: {issue.fontSize}</p>
                        <p>Font Weight: {issue.fontWeight}</p>
                      </div>
                    </div>
                    
                    <div className="p-4 bg-[#252a3a] border-t border-gray-700">
                      <h3 className="font-semibold mb-2">How to Fix:</h3>
                      <div className="bg-[#1a1f2e] p-3 rounded font-mono text-sm whitespace-pre-wrap">
{`Fix any of the following:
  Element has insufficient color contrast of ${issue.contrastRatio} (foreground color: ${issue.foregroundColor}, background color: ${issue.backgroundColor}, font size: ${issue.fontSize}, font weight: ${issue.fontWeight}).
  Expected contrast ratio of ${issue.expectedRatio}:1`}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
            
            {/* Serious Issues Section */}
            {scanResult.issues.filter(issue => issue.impact === 'serious').length > 0 && (
              <div className="mb-8">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold">Serious Issues</h2>
                  <span className="bg-orange-500 text-white text-xs px-2 py-1 rounded">
                    {scanResult.issues.filter(issue => issue.impact === 'serious').length} Serious
                  </span>
                </div>
                
                {scanResult.issues.filter(issue => issue.impact === 'serious').map((issue, index) => (
                  <div key={index} className="bg-[#2d3548] rounded-md mb-4 overflow-hidden">
                    <div className="bg-[#3a4257] p-3">
                      <div className="flex items-center">
                        <span className="bg-orange-500 text-white text-xs px-2 py-1 rounded mr-3">Serious</span>
                        <span className="text-gray-300 text-sm">Element: Element</span>
                      </div>
                      <p className="mt-2">{issue.help}</p>
                      <p className="text-gray-400 text-sm mt-1">Affected element: <code className="bg-[#1a1f2e] px-1 py-0.5 rounded text-xs">{issue.element}</code></p>
                    </div>
                    
                    <div className="p-4 bg-[#252a3a] border-t border-gray-700">
                      <h3 className="font-semibold mb-2">How to Fix:</h3>
                      <p className="text-sm">See <a href={issue.helpUrl} target="_blank" rel="noopener noreferrer" className="text-indigo-400 hover:text-indigo-300">accessibility guidelines</a> for more information.</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
            
            {/* Moderate Issues Section */}
            {scanResult.issues.filter(issue => issue.impact === 'moderate').length > 0 && (
              <div className="mb-8">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold">Moderate Issues</h2>
                  <span className="bg-yellow-500 text-white text-xs px-2 py-1 rounded">
                    {scanResult.issues.filter(issue => issue.impact === 'moderate').length} Moderate
                  </span>
                </div>
                
                {scanResult.issues.filter(issue => issue.impact === 'moderate').map((issue, index) => (
                  <div key={index} className="bg-[#2d3548] rounded-md mb-4 overflow-hidden">
                    <div className="bg-[#3a4257] p-3">
                      <div className="flex items-center">
                        <span className="bg-yellow-500 text-white text-xs px-2 py-1 rounded mr-3">Moderate</span>
                        <span className="text-gray-300 text-sm">Element: Element</span>
                      </div>
                      <p className="mt-2">{issue.help}</p>
                    </div>
                    
                    <div className="p-4 bg-[#252a3a] border-t border-gray-700">
                      <h3 className="font-semibold mb-2">How to Fix:</h3>
                      <p className="text-sm">See <a href={issue.helpUrl} target="_blank" rel="noopener noreferrer" className="text-indigo-400 hover:text-indigo-300">accessibility guidelines</a> for more information.</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
            
            {/* CTA Section */}
            <div className="bg-[#252a3a] p-6 rounded-lg border border-gray-700 text-center">
              <h2 className="text-xl font-bold mb-4">Need a comprehensive audit?</h2>
              <p className="mb-6">Get a detailed report with expert recommendations to fix all accessibility issues</p>
              <Link href="/pricing" className="bg-indigo-600 text-white px-6 py-3 rounded-md hover:bg-indigo-700 inline-block">
                View Pricing Options
              </Link>
            </div>
          </>
        ) : (
          <div className="bg-[#252a3a] p-6 rounded-lg border border-gray-700 text-center">
            <h2 className="text-xl font-bold mb-4">No Results Found</h2>
            <p className="mb-6">Please return to the homepage and scan a website</p>
            <Link href="/" className="bg-indigo-600 text-white px-6 py-3 rounded-md hover:bg-indigo-700 inline-block">
              Back to Homepage
            </Link>
          </div>
        )}
      </main>
      
      {/* Footer */}
      <footer className="py-8 px-8 text-center text-gray-400 border-t border-gray-800">
        <p>&copy; {new Date().getFullYear()} Samya. All rights reserved.</p>
      </footer>
    </div>
  );
}

export default function Results() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-[#1a1f2e] text-white flex justify-center items-center">
        <div className="flex flex-col items-center">
          <svg className="animate-spin h-10 w-10 text-indigo-500 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <p>Loading results...</p>
        </div>
      </div>
    }>
      <ResultsContent />
    </Suspense>
  );
}