{"name": "samya-next-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "playwright test", "test:accessibility": "playwright test tests/accessibility/", "test:accessibility:axe": "playwright test tests/accessibility/wcag-base.spec.ts", "test:accessibility:custom": "playwright test tests/accessibility/wcag-custom.spec.ts", "test:accessibility:keyboard": "playwright test tests/accessibility/wcag-keyboard.spec.ts", "test:accessibility:responsive": "playwright test tests/accessibility/wcag-responsive.spec.ts", "test:accessibility:manual": "playwright test tests/accessibility/wcag-manual-checklist.spec.ts", "test:accessibility:report": "playwright show-report", "test:install": "playwright install"}, "dependencies": {"next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@axe-core/playwright": "^4.10.1", "@eslint/eslintrc": "^3", "@playwright/test": "^1.52.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "axe-core": "^4.10.3", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5", "wait-on": "^8.0.3"}}